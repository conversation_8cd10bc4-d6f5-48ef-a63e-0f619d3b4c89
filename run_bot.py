#!/usr/bin/env python3
"""
Bot runner script for the Zeitwahl Telegram bot.

This script provides a simple way to start the bot for development and testing.
Make sure to set the TELEGRAM_BOT_TOKEN environment variable before running.

Usage:
    python run_bot.py

Environment Variables:
    TELEGRAM_BOT_TOKEN: Your Telegram bot token (required)

Example:
    export TELEGRAM_BOT_TOKEN="123456789:ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijk"
    python run_bot.py
"""

import asyncio
import os
import sys
import logging

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.bot.bot import TelegramBot

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def main():
    """Main function to run the bot."""
    # Check if token is provided
    token = os.getenv("TELEGRAM_BOT_TOKEN")
    if not token:
        logger.error("TELEGRAM_BOT_TOKEN environment variable is required!")
        logger.error("Please set it with: export TELEGRAM_BOT_TOKEN='your_bot_token_here'")
        sys.exit(1)
    
    logger.info("Starting Zeitwahl Telegram Bot...")
    logger.info("Press Ctrl+C to stop the bot")
    
    try:
        # Create and start the bot
        bot = TelegramBot(token=token)
        await bot.start_polling()
    except KeyboardInterrupt:
        logger.info("Bot stopped by user (Ctrl+C)")
    except Exception as e:
        logger.error(f"Bot error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
