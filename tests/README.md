# Zeitwahl Test Suite

This directory contains the comprehensive test suite for the Zeitwahl application, organized following best practices and the KISS (Keep It Simple) principle.

## Test Structure

```
tests/
├── README.md                 # This file
├── run_tests.py             # Test runner script
├── __init__.py              # Test package init
├── unit/                    # Unit tests
│   ├── __init__.py
│   └── model/               # Model unit tests
│       ├── __init__.py
│       ├── test_user.py         # User model tests (15 tests)
│       ├── test_conversation.py # Conversation model tests (14 tests)
│       ├── test_event.py        # Event model tests (15 tests)
│       ├── test_message.py      # Message model tests (10 tests)
│       ├── test_calendar_link.py # CalendarLink model tests (13 tests)
│       └── test_validation.py   # Validation utility tests (23 tests)
├── integration/             # Integration tests (future)
└── e2e/                     # End-to-end tests (future)
```

## Running Tests

### Using the Test Runner

The simplest way to run tests is using our custom test runner:

```bash
# Run all tests
python tests/run_tests.py

# Run specific model tests
python tests/run_tests.py user
python tests/run_tests.py conversation
python tests/run_tests.py event
python tests/run_tests.py message
python tests/run_tests.py calendar
python tests/run_tests.py validation

# Run with minimal output
python tests/run_tests.py --quiet
```

### Running Individual Test Files

You can also run individual test files directly:

```bash
# Run user model tests
python tests/unit/model/test_user.py

# Run conversation model tests
python tests/unit/model/test_conversation.py

# Run event model tests
python tests/unit/model/test_event.py

# Run message model tests
python tests/unit/model/test_message.py

# Run calendar link model tests
python tests/unit/model/test_calendar_link.py

# Run validation tests
python tests/unit/model/test_validation.py
```

### Using pytest (if available)

If you have pytest installed, you can use it for more advanced features:

```bash
# Run all tests with pytest
pytest tests/

# Run with verbose output
pytest -v tests/

# Run specific test file
pytest tests/unit/model/test_user.py

# Run specific test method
pytest tests/unit/model/test_user.py::TestUser::test_user_creation_with_required_fields
```

## Test Categories

### Unit Tests (`tests/unit/`)

Unit tests focus on testing individual components in isolation. Each test file corresponds to a specific module or class.

#### Model Tests (`tests/unit/model/`)

- **test_user.py**: Tests for User and UserData models
  - Valid user creation with required/optional fields
  - Validation failures for missing required fields
  - Type handling and edge cases
  - Serialization to dictionary
  - Pydantic validation rules

- **test_conversation.py**: Tests for Conversation and ConversationData models
  - Valid conversation creation
  - Required field validation
  - Context handling and mutability
  - Edge cases with large IDs and long titles

- **test_event.py**: Tests for Event and EventData models (15 tests)
  - Event creation with various field combinations
  - Required field validation
  - Recurrence logic testing
  - Date/time handling
  - Pydantic validation for complex fields

- **test_message.py**: Tests for Message and MessageData models (10 tests)
  - Message creation with required/optional fields
  - Direction and message type validation
  - Conversation relationship validation
  - Message constants and serialization

- **test_calendar_link.py**: Tests for CalendarLink and CalendarLinkData models (13 tests)
  - Calendar link creation and OAuth token handling
  - Required field validation (user_id, tokens)
  - Token expiry checking logic
  - Calendar list validation
  - Sync frequency validation

- **test_validation.py**: Tests for validation utility functions (23 tests)
  - Basic validation functions (positive integers, non-empty strings)
  - Advanced validation with constraints
  - Edge cases and boundary conditions
  - Unicode and special character handling

## Test Design Principles

### KISS (Keep It Simple)

- **Clear test names**: Each test method name clearly describes what it tests
- **Single responsibility**: Each test focuses on one specific behavior
- **Simple assertions**: Tests use straightforward assertions that are easy to understand
- **Minimal setup**: Tests avoid complex setup and use simple, realistic data

### Comprehensive Coverage

- **Success cases**: Test valid inputs and expected behavior
- **Failure cases**: Test invalid inputs and error conditions
- **Edge cases**: Test boundary conditions and unusual inputs
- **Type safety**: Test type validation and handling

### Realistic Testing

- **Real data**: Tests use realistic data that mirrors actual usage
- **Error scenarios**: Tests cover common error conditions users might encounter
- **Integration points**: Tests verify that models work correctly with their validation layers

## Test Data Patterns

### Valid Test Data

Tests use realistic data that represents actual application usage:

```python
# Realistic user data
user = User(
    telegram_id=12345,
    username="testuser",
    first_name="John",
    last_name="Doe",
    language_code="en"
)

# Realistic event data
event = Event(
    user_id=1,
    title="Team Meeting",
    start_time=datetime(2024, 6, 15, 14, 30),
    location="Conference Room A"
)
```

### Error Testing

Tests systematically verify error conditions:

```python
# Test missing required fields
with pytest.raises(ValueError, match="telegram_id is required"):
    User()

# Test invalid data types
with pytest.raises(ValueError, match="telegram_id must be a positive integer"):
    UserData(telegram_id=-1)
```

## Adding New Tests

When adding new tests, follow these guidelines:

1. **File naming**: Use `test_<module_name>.py` format
2. **Class naming**: Use `Test<ClassName>` format
3. **Method naming**: Use descriptive names like `test_<action>_<condition>`
4. **Documentation**: Add docstrings explaining what each test verifies
5. **Coverage**: Include both success and failure cases
6. **Independence**: Ensure tests don't depend on each other

### Example Test Structure

```python
class TestNewModel:
    """Test cases for NewModel."""
    
    def test_creation_with_required_fields(self):
        """Test successful creation with only required fields."""
        # Test implementation
        pass
    
    def test_creation_missing_required_field(self):
        """Test that creation fails without required field."""
        # Test implementation
        pass
    
    def test_validation_edge_cases(self):
        """Test validation with edge case values."""
        # Test implementation
        pass
```

## Continuous Integration

These tests are designed to run in any Python environment without external dependencies (except for the application code itself). The test runner provides clear output and proper exit codes for CI/CD integration.

## Future Enhancements

- **Integration tests**: Test interactions between models and repositories
- **End-to-end tests**: Test complete user workflows
- **Performance tests**: Test model performance with large datasets
- **Property-based testing**: Use hypothesis for generating test cases
- **Coverage reporting**: Add code coverage measurement
