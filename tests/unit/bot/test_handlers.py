"""
Unit tests for MessageHandler.

Tests message handling, user identification, and response generation.
Following KISS principle with clear, focused test cases.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch

# Import the classes we're testing
try:
    from app.bot.handlers import MessageHandler
    from app.bot.user_service import UserService
except ImportError:
    # Fallback for when running tests without proper environment
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))
    from app.bot.handlers import MessageHandler
    from app.bot.user_service import UserService


class TestMessageHandler:
    """Test cases for MessageHandler class."""
    
    def setup_method(self):
        """Set up test fixtures before each test method."""
        self.handler = MessageHandler()
    
    def test_initialization(self):
        """Test that MessageHandler initializes correctly."""
        assert isinstance(self.handler.user_service, UserService)
        assert self.handler.router is not None
    
    @pytest.mark.asyncio
    async def test_handle_message_success(self):
        """Test successful message handling with user identification."""
        # Create mock Telegram user
        mock_telegram_user = Mock()
        mock_telegram_user.id = 123456789
        mock_telegram_user.first_name = "John"
        mock_telegram_user.username = "johndoe"
        
        # Create mock message
        mock_message = Mock()
        mock_message.from_user = mock_telegram_user
        mock_message.text = "Hello bot"
        mock_message.reply = AsyncMock()
        
        # Mock user service methods
        with patch.object(self.handler.user_service, 'identify_user_from_message') as mock_identify:
            with patch.object(self.handler.user_service, 'create_user_from_telegram') as mock_create:
                with patch.object(self.handler.user_service, 'format_greeting_message') as mock_format:
                    
                    mock_identify.return_value = mock_telegram_user
                    mock_create.return_value = Mock()  # Mock User object
                    mock_format.return_value = "Hello John"
                    
                    # Handle the message
                    await self.handler.handle_message(mock_message)
                    
                    # Verify method calls
                    mock_identify.assert_called_once_with(mock_message)
                    mock_create.assert_called_once_with(mock_telegram_user)
                    mock_format.assert_called_once_with(mock_telegram_user)
                    
                    # Verify reply was sent
                    mock_message.reply.assert_called_once_with("Hello John")
    
    @pytest.mark.asyncio
    async def test_handle_message_no_user(self):
        """Test message handling when user cannot be identified."""
        # Create mock message
        mock_message = Mock()
        mock_message.reply = AsyncMock()
        
        # Mock user service to return None (no user identified)
        with patch.object(self.handler.user_service, 'identify_user_from_message') as mock_identify:
            mock_identify.return_value = None
            
            # Handle the message
            await self.handler.handle_message(mock_message)
            
            # Verify error message was sent
            mock_message.reply.assert_called_once_with("Sorry, I couldn't identify you.")
    
    @pytest.mark.asyncio
    async def test_handle_start_command_success(self):
        """Test successful /start command handling."""
        # Create mock Telegram user
        mock_telegram_user = Mock()
        mock_telegram_user.id = 123456789
        mock_telegram_user.first_name = "Alice"
        mock_telegram_user.username = "alice123"
        
        # Create mock message
        mock_message = Mock()
        mock_message.from_user = mock_telegram_user
        mock_message.text = "/start"
        mock_message.reply = AsyncMock()
        
        # Mock user service methods
        with patch.object(self.handler.user_service, 'identify_user_from_message') as mock_identify:
            with patch.object(self.handler.user_service, 'create_user_from_telegram') as mock_create:
                with patch.object(self.handler.user_service, 'format_greeting_message') as mock_format:
                    
                    mock_identify.return_value = mock_telegram_user
                    mock_create.return_value = Mock()  # Mock User object
                    mock_format.return_value = "Hello Alice"
                    
                    # Handle the start command
                    await self.handler.handle_start_command(mock_message)
                    
                    # Verify method calls
                    mock_identify.assert_called_once_with(mock_message)
                    mock_create.assert_called_once_with(mock_telegram_user)
                    mock_format.assert_called_once_with(mock_telegram_user)
                    
                    # Verify welcome message was sent
                    expected_message = "Hello Alice! Welcome to Zeitwahl bot. Send me any message and I'll greet you!"
                    mock_message.reply.assert_called_once_with(expected_message)
    
    @pytest.mark.asyncio
    async def test_handle_start_command_no_user(self):
        """Test /start command handling when user cannot be identified."""
        # Create mock message
        mock_message = Mock()
        mock_message.reply = AsyncMock()
        
        # Mock user service to return None (no user identified)
        with patch.object(self.handler.user_service, 'identify_user_from_message') as mock_identify:
            mock_identify.return_value = None
            
            # Handle the start command
            await self.handler.handle_start_command(mock_message)
            
            # Verify error message was sent
            mock_message.reply.assert_called_once_with("Sorry, I couldn't identify you.")
    
    def test_get_router(self):
        """Test that get_router returns the configured router."""
        router = self.handler.get_router()
        assert router is not None
        assert router == self.handler.router


class TestMessageHandlerIntegration:
    """Integration tests for MessageHandler with real UserService."""
    
    def setup_method(self):
        """Set up test fixtures before each test method."""
        self.handler = MessageHandler()
    
    @pytest.mark.asyncio
    async def test_handle_message_integration(self):
        """Test message handling with real UserService integration."""
        # Create mock Telegram user
        mock_telegram_user = Mock()
        mock_telegram_user.id = 123456789
        mock_telegram_user.first_name = "Bob"
        mock_telegram_user.last_name = "Smith"
        mock_telegram_user.username = "bobsmith"
        mock_telegram_user.language_code = "en"
        
        # Create mock message
        mock_message = Mock()
        mock_message.from_user = mock_telegram_user
        mock_message.text = "Hi there"
        mock_message.reply = AsyncMock()
        
        # Handle the message (using real UserService)
        await self.handler.handle_message(mock_message)
        
        # Verify greeting was sent
        mock_message.reply.assert_called_once_with("Hello Bob")
    
    @pytest.mark.asyncio
    async def test_handle_start_command_integration(self):
        """Test /start command with real UserService integration."""
        # Create mock Telegram user
        mock_telegram_user = Mock()
        mock_telegram_user.id = 987654321
        mock_telegram_user.first_name = None
        mock_telegram_user.last_name = None
        mock_telegram_user.username = "testuser"
        mock_telegram_user.language_code = "es"
        
        # Create mock message
        mock_message = Mock()
        mock_message.from_user = mock_telegram_user
        mock_message.text = "/start"
        mock_message.reply = AsyncMock()
        
        # Handle the start command (using real UserService)
        await self.handler.handle_start_command(mock_message)
        
        # Verify welcome message was sent with username fallback
        expected_message = "Hello testuser! Welcome to Zeitwahl bot. Send me any message and I'll greet you!"
        mock_message.reply.assert_called_once_with(expected_message)


if __name__ == "__main__":
    # Run tests if this file is executed directly
    pytest.main([__file__])
