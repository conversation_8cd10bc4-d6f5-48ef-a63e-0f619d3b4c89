"""
Unit tests for TelegramBot.

Tests bot initialization, configuration, and basic functionality.
Following KISS principle with clear, focused test cases.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch
import os

# Import the classes we're testing
try:
    from app.bot.bot import TelegramBot
    from app.config.bot_config import BotConfig
except ImportError:
    # Fallback for when running tests without proper environment
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))
    from app.bot.bot import TelegramBot
    from app.config.bot_config import BotConfig


class TestTelegramBot:
    """Test cases for TelegramBot class."""
    
    def test_initialization_with_valid_token(self):
        """Test bot initialization with a valid token."""
        valid_token = "123456789:ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijk"
        
        with patch('app.bot.bot.Bot') as mock_bot_class:
            with patch('app.bot.bot.Dispatcher') as mock_dispatcher_class:
                mock_bot_class.return_value = Mock()
                mock_dispatcher_class.return_value = Mock()
                
                bot = TelegramBot(token=valid_token)
                
                assert bot.token == valid_token
                assert bot.bot is not None
                assert bot.dp is not None
                assert bot.message_handler is not None
    
    def test_initialization_without_token_raises_error(self):
        """Test that initialization without token raises ValueError."""
        with pytest.raises(ValueError, match="Bot token is required"):
            TelegramBot(token="")
    
    def test_initialization_with_invalid_token_raises_error(self):
        """Test that initialization with invalid token raises ValueError."""
        invalid_tokens = [
            "invalid_token",
            "123456789",
            ":ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijk",
            "123456789:",
            "not_numeric:ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijk"
        ]
        
        for invalid_token in invalid_tokens:
            with pytest.raises(ValueError, match="Invalid bot token format"):
                TelegramBot(token=invalid_token)
    
    def test_validate_token_valid_tokens(self):
        """Test token validation with valid tokens."""
        valid_token = "123456789:ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijk"
        
        with patch('app.bot.bot.Bot'):
            with patch('app.bot.bot.Dispatcher'):
                bot = TelegramBot(token=valid_token)
                
                # Test various valid token formats
                valid_tokens = [
                    "123456789:ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijk",
                    "987654321:XYZabcdefghijklmnopqrstuvwxyz123456789",
                    "111111111:very_long_token_string_here_123456789"
                ]
                
                for token in valid_tokens:
                    assert bot._validate_token(token) is True
    
    def test_validate_token_invalid_tokens(self):
        """Test token validation with invalid tokens."""
        valid_token = "123456789:ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijk"
        
        with patch('app.bot.bot.Bot'):
            with patch('app.bot.bot.Dispatcher'):
                bot = TelegramBot(token=valid_token)
                
                # Test various invalid token formats
                invalid_tokens = [
                    "",
                    "invalid_token",
                    "123456789",
                    ":ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijk",
                    "123456789:",
                    "not_numeric:ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijk",
                    "123456789:short"
                ]
                
                for token in invalid_tokens:
                    assert bot._validate_token(token) is False
    
    @pytest.mark.asyncio
    async def test_send_message_success(self):
        """Test successful message sending."""
        valid_token = "123456789:ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijk"
        
        with patch('app.bot.bot.Bot') as mock_bot_class:
            with patch('app.bot.bot.Dispatcher'):
                mock_bot = Mock()
                mock_bot.send_message = AsyncMock()
                mock_bot_class.return_value = mock_bot
                
                bot = TelegramBot(token=valid_token)
                
                # Test sending message
                result = await bot.send_message(chat_id=123456789, text="Hello World")
                
                assert result is True
                mock_bot.send_message.assert_called_once_with(
                    chat_id=123456789, 
                    text="Hello World"
                )
    
    @pytest.mark.asyncio
    async def test_send_message_failure(self):
        """Test message sending failure handling."""
        valid_token = "123456789:ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijk"
        
        with patch('app.bot.bot.Bot') as mock_bot_class:
            with patch('app.bot.bot.Dispatcher'):
                mock_bot = Mock()
                mock_bot.send_message = AsyncMock(side_effect=Exception("Network error"))
                mock_bot_class.return_value = mock_bot
                
                bot = TelegramBot(token=valid_token)
                
                # Test sending message with failure
                result = await bot.send_message(chat_id=123456789, text="Hello World")
                
                assert result is False
    
    @pytest.mark.asyncio
    async def test_stop_bot(self):
        """Test bot stopping functionality."""
        valid_token = "123456789:ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijk"
        
        with patch('app.bot.bot.Bot') as mock_bot_class:
            with patch('app.bot.bot.Dispatcher'):
                mock_bot = Mock()
                mock_bot.session = Mock()
                mock_bot.session.close = AsyncMock()
                mock_bot_class.return_value = mock_bot
                
                bot = TelegramBot(token=valid_token)
                
                # Test stopping bot
                await bot.stop()
                
                mock_bot.session.close.assert_called_once()
    
    def test_register_handlers(self):
        """Test that handlers are registered correctly."""
        valid_token = "123456789:ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijk"
        
        with patch('app.bot.bot.Bot'):
            with patch('app.bot.bot.Dispatcher') as mock_dispatcher_class:
                mock_dispatcher = Mock()
                mock_dispatcher.include_router = Mock()
                mock_dispatcher_class.return_value = mock_dispatcher
                
                bot = TelegramBot(token=valid_token)
                
                # Verify that include_router was called
                mock_dispatcher.include_router.assert_called_once()


class TestTelegramBotConfiguration:
    """Test cases for bot configuration handling."""
    
    def test_bot_uses_config_token_when_none_provided(self):
        """Test that bot uses config token when none is provided."""
        valid_token = "123456789:ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijk"
        
        with patch('app.bot.bot.Bot'):
            with patch('app.bot.bot.Dispatcher'):
                with patch('app.bot.bot.bot_config') as mock_config:
                    mock_config.token = valid_token
                    mock_config.validate_token.return_value = True
                    
                    bot = TelegramBot()
                    
                    assert bot.token == valid_token
    
    def test_bot_config_validation(self):
        """Test bot configuration validation."""
        config = BotConfig()
        
        # Test with valid token
        config.token = "123456789:ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijk"
        assert config.validate_token() is True
        
        # Test with invalid token
        config.token = "invalid"
        assert config.validate_token() is False
        
        # Test with empty token
        config.token = ""
        assert config.validate_token() is False


if __name__ == "__main__":
    # Run tests if this file is executed directly
    pytest.main([__file__])
