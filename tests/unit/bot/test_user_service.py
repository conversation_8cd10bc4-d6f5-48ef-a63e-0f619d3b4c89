"""
Unit tests for UserService.

Tests user identification, user creation, and greeting message formatting.
Following KISS principle with clear, focused test cases.
"""

import pytest
from datetime import datetime
from unittest.mock import Mock

# Import the classes we're testing
try:
    from app.bot.user_service import UserService
    from app.model.user import User
except ImportError:
    # Fallback for when running tests without proper environment
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))
    from app.bot.user_service import UserService
    from app.model.user import User


class TestUserService:
    """Test cases for UserService class."""
    
    def setup_method(self):
        """Set up test fixtures before each test method."""
        self.user_service = UserService()
    
    def test_identify_user_from_message_success(self):
        """Test successful user identification from message."""
        # Create mock Telegram user
        mock_telegram_user = Mock()
        mock_telegram_user.id = 123456789
        mock_telegram_user.username = "testuser"
        mock_telegram_user.first_name = "Test"
        mock_telegram_user.last_name = "User"
        mock_telegram_user.language_code = "en"
        
        # Create mock message with user
        mock_message = Mock()
        mock_message.from_user = mock_telegram_user
        
        # Test user identification
        result = self.user_service.identify_user_from_message(mock_message)
        
        assert result is not None
        assert result.id == 123456789
        assert result.username == "testuser"
        assert result.first_name == "Test"
    
    def test_identify_user_from_message_no_user(self):
        """Test user identification when message has no user."""
        # Create mock message without user
        mock_message = Mock()
        mock_message.from_user = None
        
        # Test user identification
        result = self.user_service.identify_user_from_message(mock_message)
        
        assert result is None
    
    def test_identify_user_from_message_none_message(self):
        """Test user identification with None message."""
        result = self.user_service.identify_user_from_message(None)
        assert result is None
    
    def test_create_user_from_telegram_complete_data(self):
        """Test creating user from complete Telegram user data."""
        # Create mock Telegram user with all data
        mock_telegram_user = Mock()
        mock_telegram_user.id = 123456789
        mock_telegram_user.username = "testuser"
        mock_telegram_user.first_name = "Test"
        mock_telegram_user.last_name = "User"
        mock_telegram_user.language_code = "en"
        
        # Create user from Telegram data
        user = self.user_service.create_user_from_telegram(mock_telegram_user)
        
        assert isinstance(user, User)
        assert user.telegram_id == 123456789
        assert user.username == "testuser"
        assert user.first_name == "Test"
        assert user.last_name == "User"
        assert user.language_code == "en"
        assert user.is_active is True
        assert user.last_interaction is not None
    
    def test_create_user_from_telegram_minimal_data(self):
        """Test creating user from minimal Telegram user data."""
        # Create mock Telegram user with minimal data
        mock_telegram_user = Mock()
        mock_telegram_user.id = 123456789
        mock_telegram_user.username = None
        mock_telegram_user.first_name = None
        mock_telegram_user.last_name = None
        mock_telegram_user.language_code = None
        
        # Create user from Telegram data
        user = self.user_service.create_user_from_telegram(mock_telegram_user)
        
        assert isinstance(user, User)
        assert user.telegram_id == 123456789
        assert user.username is None
        assert user.first_name == ""
        assert user.last_name == ""
        assert user.language_code == "en"  # Default value
        assert user.is_active is True
    
    def test_get_user_display_name_first_name(self):
        """Test getting display name when first name is available."""
        mock_telegram_user = Mock()
        mock_telegram_user.first_name = "John"
        mock_telegram_user.username = "johndoe"
        
        display_name = self.user_service.get_user_display_name(mock_telegram_user)
        assert display_name == "John"
    
    def test_get_user_display_name_username_only(self):
        """Test getting display name when only username is available."""
        mock_telegram_user = Mock()
        mock_telegram_user.first_name = None
        mock_telegram_user.username = "johndoe"
        
        display_name = self.user_service.get_user_display_name(mock_telegram_user)
        assert display_name == "johndoe"
    
    def test_get_user_display_name_fallback(self):
        """Test getting display name when no name data is available."""
        mock_telegram_user = Mock()
        mock_telegram_user.first_name = None
        mock_telegram_user.username = None
        
        display_name = self.user_service.get_user_display_name(mock_telegram_user)
        assert display_name == "User"
    
    def test_format_greeting_message(self):
        """Test formatting greeting message."""
        mock_telegram_user = Mock()
        mock_telegram_user.first_name = "Alice"
        mock_telegram_user.username = "alice123"
        
        greeting = self.user_service.format_greeting_message(mock_telegram_user)
        assert greeting == "Hello Alice"
    
    def test_format_greeting_message_username_fallback(self):
        """Test formatting greeting message with username fallback."""
        mock_telegram_user = Mock()
        mock_telegram_user.first_name = None
        mock_telegram_user.username = "alice123"
        
        greeting = self.user_service.format_greeting_message(mock_telegram_user)
        assert greeting == "Hello alice123"
    
    def test_format_greeting_message_generic_fallback(self):
        """Test formatting greeting message with generic fallback."""
        mock_telegram_user = Mock()
        mock_telegram_user.first_name = None
        mock_telegram_user.username = None
        
        greeting = self.user_service.format_greeting_message(mock_telegram_user)
        assert greeting == "Hello User"


if __name__ == "__main__":
    # Run tests if this file is executed directly
    pytest.main([__file__])
