"""
Unit tests for Message model.

Tests both successful creation and validation failures.
"""

import pytest
from datetime import datetime

try:
    from app.model.message import Message, MessageData
except ImportError:
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))
    from app.model.message import Message, MessageData


class TestMessage:
    """Test cases for Message model."""
    
    def test_message_creation_with_required_fields(self):
        """Test successful message creation with only required fields."""
        msg = Message(
            conversation_id=1,
            direction="incoming",
            message_type="text",
            content="Hello world"
        )
        
        assert msg.conversation_id == 1
        assert msg.direction == "incoming"
        assert msg.message_type == "text"
        assert msg.content == "Hello world"
        assert msg.telegram_message_id is None
        assert msg.metadata == {}
        assert msg.sent_at is None
        assert msg.is_processed is False
    
    def test_message_creation_with_all_fields(self):
        """Test successful message creation with all fields."""
        now = datetime.now()
        metadata = {"user_id": 123, "chat_type": "private"}
        
        msg = Message(
            conversation_id=2,
            telegram_message_id=456,
            direction="outgoing",
            message_type="command",
            content="/start",
            metadata=metadata,
            sent_at=now,
            is_processed=True
        )
        
        assert msg.conversation_id == 2
        assert msg.telegram_message_id == 456
        assert msg.direction == "outgoing"
        assert msg.message_type == "command"
        assert msg.content == "/start"
        assert msg.metadata == metadata
        assert msg.sent_at == now
        assert msg.is_processed is True
    
    def test_message_creation_missing_conversation_id(self):
        """Test that message creation fails without conversation_id."""
        with pytest.raises(ValueError, match="conversation_id is required"):
            Message(direction="incoming", message_type="text")
    
    def test_message_creation_none_conversation_id(self):
        """Test that message creation fails with None conversation_id."""
        with pytest.raises(ValueError, match="conversation_id is required"):
            Message(conversation_id=None, direction="incoming", message_type="text")
    
    def test_message_constants(self):
        """Test that message constants are properly defined."""
        assert Message.DIRECTION_INCOMING == "incoming"
        assert Message.DIRECTION_OUTGOING == "outgoing"
        assert Message.TYPE_TEXT == "text"
        assert Message.TYPE_COMMAND == "command"
        assert Message.TYPE_MEDIA == "media"
        assert Message.TYPE_CALLBACK == "callback"
        assert Message.TYPE_LOCATION == "location"
        assert Message.TYPE_CONTACT == "contact"
    
    def test_message_to_dict(self):
        """Test message serialization to dictionary."""
        now = datetime.now()
        msg = Message(
            conversation_id=1,
            telegram_message_id=123,
            direction="incoming",
            message_type="text",
            content="Test message",
            sent_at=now
        )
        
        msg_dict = msg.to_dict()
        
        assert isinstance(msg_dict, dict)
        assert msg_dict["conversation_id"] == 1
        assert msg_dict["telegram_message_id"] == 123
        assert msg_dict["direction"] == "incoming"
        assert msg_dict["message_type"] == "text"
        assert msg_dict["content"] == "Test message"
        assert msg_dict["sent_at"] == now.isoformat()
        assert "id" in msg_dict
        assert "created_at" in msg_dict
        assert "updated_at" in msg_dict


class TestMessageData:
    """Test cases for MessageData Pydantic model."""
    
    def test_messagedata_creation_valid(self):
        """Test successful MessageData creation with valid data."""
        msg_data = MessageData(
            conversation_id=1,
            direction="incoming",
            message_type="text"
        )
        
        assert msg_data.conversation_id == 1
        assert msg_data.direction == "incoming"
        assert msg_data.message_type == "text"
        assert msg_data.content == ""
        assert msg_data.telegram_message_id is None
        assert msg_data.metadata == {}
        assert msg_data.sent_at is None
        assert msg_data.is_processed is False
    
    def test_messagedata_direction_validation(self):
        """Test direction validation."""
        # Valid directions
        for direction in ["incoming", "outgoing"]:
            msg_data = MessageData(
                conversation_id=1,
                direction=direction,
                message_type="text"
            )
            assert msg_data.direction == direction
        
        # Invalid direction should fail
        with pytest.raises(ValueError, match="direction must be one of"):
            MessageData(
                conversation_id=1,
                direction="invalid",
                message_type="text"
            )
    
    def test_messagedata_message_type_validation(self):
        """Test message type validation."""
        # Valid message types
        valid_types = ["text", "command", "media", "callback", "location", "contact"]
        for msg_type in valid_types:
            msg_data = MessageData(
                conversation_id=1,
                direction="incoming",
                message_type=msg_type
            )
            assert msg_data.message_type == msg_type
        
        # Invalid message type should fail
        with pytest.raises(ValueError, match="message_type must be one of"):
            MessageData(
                conversation_id=1,
                direction="incoming",
                message_type="invalid"
            )
    
    def test_message_from_pydantic(self):
        """Test creating Message from MessageData."""
        now = datetime.now()
        msg_data = MessageData(
            conversation_id=1,
            direction="incoming",
            message_type="text",
            content="Test message"
        )
        
        msg = Message.from_pydantic(
            msg_data,
            id=1,
            created_at=now
        )
        
        assert msg.conversation_id == 1
        assert msg.direction == "incoming"
        assert msg.message_type == "text"
        assert msg.content == "Test message"
        assert msg.id == 1
        assert msg.created_at == now


# Test runner for when running this file directly
if __name__ == "__main__":
    import traceback
    
    def run_test_method(test_class, method_name):
        """Run a single test method."""
        try:
            instance = test_class()
            method = getattr(instance, method_name)
            method()
            print(f"✓ {test_class.__name__}.{method_name}")
            return True
        except Exception as e:
            print(f"✗ {test_class.__name__}.{method_name}: {e}")
            traceback.print_exc()
            return False
    
    def run_all_tests():
        """Run all test methods."""
        test_classes = [TestMessage, TestMessageData]
        total_tests = 0
        passed_tests = 0
        
        print("Running Message model tests...")
        print("=" * 50)
        
        for test_class in test_classes:
            methods = [m for m in dir(test_class) if m.startswith('test_')]
            for method_name in methods:
                total_tests += 1
                if run_test_method(test_class, method_name):
                    passed_tests += 1
        
        print("=" * 50)
        print(f"Results: {passed_tests}/{total_tests} tests passed")
        return passed_tests == total_tests
    
    if run_all_tests():
        print("All tests passed! ✓")
    else:
        print("Some tests failed! ✗")
        exit(1)
