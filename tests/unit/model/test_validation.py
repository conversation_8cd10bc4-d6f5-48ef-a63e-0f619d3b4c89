"""
Unit tests for validation utilities.

Tests the simple validation functions following KISS principle.
"""

import pytest
from datetime import datetime

try:
    from app.model.validation import (
        validate_positive_integer,
        validate_non_empty_string,
        validate_datetime_not_past,
        validate_timezone,
        validate_language_code,
        validate_integer,
        validate_string,
        validate_boolean
    )
except ImportError:
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))
    from app.model.validation import (
        validate_positive_integer,
        validate_non_empty_string,
        validate_datetime_not_past,
        validate_timezone,
        validate_language_code,
        validate_integer,
        validate_string,
        validate_boolean
    )


class TestBasicValidation:
    """Test basic validation functions."""
    
    def test_validate_positive_integer_success(self):
        """Test positive integer validation with valid values."""
        assert validate_positive_integer(1, "test_field") == 1
        assert validate_positive_integer(100, "test_field") == 100
        assert validate_positive_integer(999999, "test_field") == 999999
    
    def test_validate_positive_integer_failure(self):
        """Test positive integer validation with invalid values."""
        with pytest.raises(ValueError, match="test_field must be a positive integer"):
            validate_positive_integer(0, "test_field")
        
        with pytest.raises(ValueError, match="test_field must be a positive integer"):
            validate_positive_integer(-1, "test_field")
        
        with pytest.raises(ValueError, match="test_field must be a positive integer"):
            validate_positive_integer(-100, "test_field")
    
    def test_validate_non_empty_string_success(self):
        """Test non-empty string validation with valid values."""
        assert validate_non_empty_string("hello", "test_field") == "hello"
        assert validate_non_empty_string("  hello  ", "test_field") == "hello"
        assert validate_non_empty_string("a", "test_field") == "a"
    
    def test_validate_non_empty_string_failure(self):
        """Test non-empty string validation with invalid values."""
        with pytest.raises(ValueError, match="test_field cannot be empty"):
            validate_non_empty_string("", "test_field")
        
        with pytest.raises(ValueError, match="test_field cannot be empty"):
            validate_non_empty_string("   ", "test_field")
        
        with pytest.raises(ValueError, match="test_field cannot be empty"):
            validate_non_empty_string(None, "test_field")
    
    def test_validate_timezone_success(self):
        """Test timezone validation with valid values."""
        assert validate_timezone("UTC") == "UTC"
        assert validate_timezone("Europe/London") == "Europe/London"
        assert validate_timezone("America/New_York") == "America/New_York"
    
    def test_validate_timezone_defaults(self):
        """Test timezone validation defaults."""
        assert validate_timezone("") == "UTC"
        assert validate_timezone(None) == "UTC"
    
    def test_validate_language_code_success(self):
        """Test language code validation with valid values."""
        assert validate_language_code("en") == "en"
        assert validate_language_code("EN") == "en"
        assert validate_language_code("de") == "de"
        assert validate_language_code("fr") == "fr"
        assert validate_language_code("spa") == "spa"
    
    def test_validate_language_code_defaults(self):
        """Test language code validation defaults."""
        assert validate_language_code("") == "en"
        assert validate_language_code(None) == "en"
    
    def test_validate_language_code_failure(self):
        """Test language code validation with invalid values."""
        with pytest.raises(ValueError, match="Language code must be 2-3 characters"):
            validate_language_code("a")
        
        with pytest.raises(ValueError, match="Language code must be 2-3 characters"):
            validate_language_code("toolong")


class TestAdvancedValidation:
    """Test advanced validation functions."""
    
    def test_validate_integer_success(self):
        """Test integer validation with valid values."""
        assert validate_integer(42, "test_field") == 42
        assert validate_integer(0, "test_field") == 0
        assert validate_integer(-10, "test_field") == -10
        assert validate_integer(None, "test_field") is None
    
    def test_validate_integer_with_range(self):
        """Test integer validation with min/max values."""
        assert validate_integer(5, "test_field", min_value=1, max_value=10) == 5
        assert validate_integer(1, "test_field", min_value=1) == 1
        assert validate_integer(10, "test_field", max_value=10) == 10
    
    def test_validate_integer_failure(self):
        """Test integer validation failures."""
        with pytest.raises(ValueError, match="test_field must be an integer"):
            validate_integer("not_int", "test_field")
        
        with pytest.raises(ValueError, match="test_field must be an integer"):
            validate_integer(3.14, "test_field")
        
        with pytest.raises(ValueError, match="test_field must be an integer"):
            validate_integer(True, "test_field")  # bool is instance of int, but we exclude it
    
    def test_validate_integer_range_failure(self):
        """Test integer validation range failures."""
        with pytest.raises(ValueError, match="test_field must be at least 5"):
            validate_integer(3, "test_field", min_value=5)
        
        with pytest.raises(ValueError, match="test_field must be at most 10"):
            validate_integer(15, "test_field", max_value=10)
    
    def test_validate_string_success(self):
        """Test string validation with valid values."""
        assert validate_string("hello", "test_field") == "hello"
        assert validate_string("", "test_field") == ""
        assert validate_string(None, "test_field") is None
    
    def test_validate_string_with_length(self):
        """Test string validation with length constraints."""
        assert validate_string("hello", "test_field", min_length=3, max_length=10) == "hello"
        assert validate_string("hi", "test_field", min_length=2) == "hi"
        assert validate_string("short", "test_field", max_length=10) == "short"
    
    def test_validate_string_failure(self):
        """Test string validation failures."""
        with pytest.raises(ValueError, match="test_field must be a string"):
            validate_string(123, "test_field")
        
        with pytest.raises(ValueError, match="test_field must be a string"):
            validate_string([], "test_field")
    
    def test_validate_string_length_failure(self):
        """Test string validation length failures."""
        with pytest.raises(ValueError, match="test_field must be at least 5 characters long"):
            validate_string("hi", "test_field", min_length=5)
        
        with pytest.raises(ValueError, match="test_field must be at most 3 characters long"):
            validate_string("toolong", "test_field", max_length=3)
    
    def test_validate_boolean_success(self):
        """Test boolean validation with valid values."""
        assert validate_boolean(True, "test_field") is True
        assert validate_boolean(False, "test_field") is False
        assert validate_boolean(None, "test_field") is None
    
    def test_validate_boolean_failure(self):
        """Test boolean validation failures."""
        with pytest.raises(ValueError, match="test_field must be a boolean"):
            validate_boolean("true", "test_field")
        
        with pytest.raises(ValueError, match="test_field must be a boolean"):
            validate_boolean(1, "test_field")
        
        with pytest.raises(ValueError, match="test_field must be a boolean"):
            validate_boolean(0, "test_field")


class TestEdgeCases:
    """Test edge cases and boundary conditions."""
    
    def test_large_numbers(self):
        """Test validation with very large numbers."""
        large_num = 999999999999999
        assert validate_positive_integer(large_num, "test") == large_num
        assert validate_integer(large_num, "test") == large_num
    
    def test_unicode_strings(self):
        """Test validation with unicode strings."""
        unicode_str = "Hello 世界 🌍"
        assert validate_non_empty_string(unicode_str, "test") == unicode_str
        assert validate_string(unicode_str, "test") == unicode_str
    
    def test_empty_and_whitespace(self):
        """Test various empty and whitespace scenarios."""
        # Empty string
        assert validate_string("", "test") == ""
        
        # Whitespace string
        assert validate_string("   ", "test") == "   "
        
        # But non-empty validation should strip and fail on whitespace
        with pytest.raises(ValueError):
            validate_non_empty_string("   ", "test")
    
    def test_boundary_values(self):
        """Test boundary values for ranges."""
        # Exactly at boundaries
        assert validate_integer(5, "test", min_value=5, max_value=5) == 5
        assert validate_string("hello", "test", min_length=5, max_length=5) == "hello"
        
        # Just outside boundaries should fail
        with pytest.raises(ValueError):
            validate_integer(4, "test", min_value=5)
        
        with pytest.raises(ValueError):
            validate_integer(6, "test", max_value=5)


# Test runner for when running this file directly
if __name__ == "__main__":
    import traceback
    
    def run_test_method(test_class, method_name):
        """Run a single test method."""
        try:
            instance = test_class()
            method = getattr(instance, method_name)
            method()
            print(f"✓ {test_class.__name__}.{method_name}")
            return True
        except Exception as e:
            print(f"✗ {test_class.__name__}.{method_name}: {e}")
            traceback.print_exc()
            return False
    
    def run_all_tests():
        """Run all test methods."""
        test_classes = [TestBasicValidation, TestAdvancedValidation, TestEdgeCases]
        total_tests = 0
        passed_tests = 0
        
        print("Running Validation utility tests...")
        print("=" * 50)
        
        for test_class in test_classes:
            methods = [m for m in dir(test_class) if m.startswith('test_')]
            for method_name in methods:
                total_tests += 1
                if run_test_method(test_class, method_name):
                    passed_tests += 1
        
        print("=" * 50)
        print(f"Results: {passed_tests}/{total_tests} tests passed")
        return passed_tests == total_tests
    
    if run_all_tests():
        print("All tests passed! ✓")
    else:
        print("Some tests failed! ✗")
        exit(1)
