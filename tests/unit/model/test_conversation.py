"""
Unit tests for Conversation model.

Tests both successful creation and validation failures.
"""

import pytest
from datetime import datetime

try:
    from app.model.conversation import Conversation, ConversationData
except ImportError:
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))
    from app.model.conversation import Conversation, ConversationData


class TestConversation:
    """Test cases for Conversation model."""
    
    def test_conversation_creation_with_required_fields(self):
        """Test successful conversation creation with only required fields."""
        conv = Conversation(
            user_id=1,
            telegram_chat_id=12345
        )
        
        assert conv.user_id == 1
        assert conv.telegram_chat_id == 12345
        assert conv.title == ""
        assert conv.context == {}
        assert conv.is_active is True
        assert conv.last_message_at is None
    
    def test_conversation_creation_with_all_fields(self):
        """Test successful conversation creation with all fields."""
        now = datetime.now()
        context = {"step": "waiting_for_input", "data": {"key": "value"}}
        
        conv = Conversation(
            user_id=2,
            telegram_chat_id=67890,
            title="Test Conversation",
            context=context,
            is_active=False,
            last_message_at=now
        )
        
        assert conv.user_id == 2
        assert conv.telegram_chat_id == 67890
        assert conv.title == "Test Conversation"
        assert conv.context == context
        assert conv.is_active is False
        assert conv.last_message_at == now
    
    def test_conversation_creation_missing_user_id(self):
        """Test that conversation creation fails without user_id."""
        with pytest.raises(ValueError, match="user_id is required"):
            Conversation(telegram_chat_id=12345)

    def test_conversation_creation_missing_telegram_chat_id(self):
        """Test that conversation creation fails without telegram_chat_id."""
        with pytest.raises(ValueError, match="telegram_chat_id is required"):
            Conversation(user_id=1)

    def test_conversation_creation_missing_both_required(self):
        """Test that conversation creation fails without both required fields."""
        with pytest.raises(ValueError, match="user_id is required"):
            Conversation()

    def test_conversation_creation_none_values(self):
        """Test conversation creation with None values for required fields."""
        with pytest.raises(ValueError, match="user_id is required"):
            Conversation(user_id=None, telegram_chat_id=12345)

        with pytest.raises(ValueError, match="telegram_chat_id is required"):
            Conversation(user_id=1, telegram_chat_id=None)
    
    def test_conversation_to_dict(self):
        """Test conversation serialization to dictionary."""
        now = datetime.now()
        context = {"step": "test"}
        
        conv = Conversation(
            user_id=1,
            telegram_chat_id=12345,
            title="Test Chat",
            context=context,
            last_message_at=now
        )
        
        conv_dict = conv.to_dict()
        
        assert isinstance(conv_dict, dict)
        assert conv_dict["user_id"] == 1
        assert conv_dict["telegram_chat_id"] == 12345
        assert conv_dict["title"] == "Test Chat"
        assert conv_dict["context"] == context
        assert conv_dict["is_active"] is True
        assert conv_dict["last_message_at"] == now.isoformat()
        assert "id" in conv_dict
        assert "created_at" in conv_dict
        assert "updated_at" in conv_dict
    
    def test_conversation_to_dict_with_none_values(self):
        """Test conversation serialization with None values."""
        conv = Conversation(user_id=1, telegram_chat_id=12345)
        conv_dict = conv.to_dict()
        
        assert conv_dict["user_id"] == 1
        assert conv_dict["telegram_chat_id"] == 12345
        assert conv_dict["last_message_at"] is None
        assert conv_dict["created_at"] is None
        assert conv_dict["updated_at"] is None


class TestConversationData:
    """Test cases for ConversationData Pydantic model."""
    
    def test_conversationdata_creation_valid(self):
        """Test successful ConversationData creation with valid data."""
        conv_data = ConversationData(
            user_id=1,
            telegram_chat_id=12345
        )
        
        assert conv_data.user_id == 1
        assert conv_data.telegram_chat_id == 12345
        assert conv_data.title == ""
        assert conv_data.context == {}
        assert conv_data.is_active is True
        assert conv_data.last_message_at is None
    
    def test_conversationdata_creation_with_all_fields(self):
        """Test ConversationData creation with all fields."""
        now = datetime.now()
        context = {"current_step": "input", "user_data": {"name": "John"}}
        
        conv_data = ConversationData(
            user_id=2,
            telegram_chat_id=67890,
            title="Planning Session",
            context=context,
            is_active=False,
            last_message_at=now
        )
        
        assert conv_data.user_id == 2
        assert conv_data.telegram_chat_id == 67890
        assert conv_data.title == "Planning Session"
        assert conv_data.context == context
        assert conv_data.is_active is False
        assert conv_data.last_message_at == now
    
    def test_conversationdata_context_default_factory(self):
        """Test that context defaults to empty dict."""
        conv_data1 = ConversationData(user_id=1, telegram_chat_id=12345)
        conv_data2 = ConversationData(user_id=2, telegram_chat_id=67890)
        
        # Should be separate dict instances
        assert conv_data1.context == {}
        assert conv_data2.context == {}
        assert conv_data1.context is not conv_data2.context
    
    def test_conversation_from_pydantic(self):
        """Test creating Conversation from ConversationData."""
        now = datetime.now()
        conv_data = ConversationData(
            user_id=1,
            telegram_chat_id=12345,
            title="Test Chat",
            context={"step": "start"}
        )
        
        conv = Conversation.from_pydantic(
            conv_data, 
            id=1, 
            created_at=now
        )
        
        assert conv.user_id == 1
        assert conv.telegram_chat_id == 12345
        assert conv.title == "Test Chat"
        assert conv.context == {"step": "start"}
        assert conv.id == 1
        assert conv.created_at == now
    
    def test_conversation_context_mutability(self):
        """Test that conversation context can be modified."""
        conv = Conversation(
            user_id=1,
            telegram_chat_id=12345,
            context={"step": "initial"}
        )
        
        # Should be able to modify context
        conv.context["step"] = "updated"
        conv.context["new_key"] = "new_value"
        
        assert conv.context["step"] == "updated"
        assert conv.context["new_key"] == "new_value"
    
    def test_conversation_edge_cases(self):
        """Test conversation creation with edge case values."""
        # Very large IDs
        conv = Conversation(
            user_id=999999999,
            telegram_chat_id=-100123456789  # Telegram group chat ID format
        )
        assert conv.user_id == 999999999
        assert conv.telegram_chat_id == -100123456789
        
        # Empty title and context
        conv = Conversation(
            user_id=1,
            telegram_chat_id=12345,
            title="",
            context={}
        )
        assert conv.title == ""
        assert conv.context == {}
        
        # Very long title
        long_title = "A" * 1000
        conv = Conversation(
            user_id=1,
            telegram_chat_id=12345,
            title=long_title
        )
        assert conv.title == long_title


# Test runner for when running this file directly
if __name__ == "__main__":
    import traceback

    def run_test_method(test_class, method_name):
        """Run a single test method."""
        try:
            instance = test_class()
            method = getattr(instance, method_name)
            method()
            print(f"✓ {test_class.__name__}.{method_name}")
            return True
        except Exception as e:
            print(f"✗ {test_class.__name__}.{method_name}: {e}")
            traceback.print_exc()
            return False

    def run_all_tests():
        """Run all test methods."""
        test_classes = [TestConversation, TestConversationData]
        total_tests = 0
        passed_tests = 0

        print("Running Conversation model tests...")
        print("=" * 50)

        for test_class in test_classes:
            methods = [m for m in dir(test_class) if m.startswith('test_')]
            for method_name in methods:
                total_tests += 1
                if run_test_method(test_class, method_name):
                    passed_tests += 1

        print("=" * 50)
        print(f"Results: {passed_tests}/{total_tests} tests passed")
        return passed_tests == total_tests

    if run_all_tests():
        print("All tests passed! ✓")
    else:
        print("Some tests failed! ✗")
        exit(1)
