"""
Unit tests for Event model.

Tests both successful creation, validation failures, and recurrence logic.
"""

import pytest
from datetime import datetime, timedelta

try:
    from app.model.event import Event, EventData, RecurrenceType
except ImportError:
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))
    from app.model.event import Event, EventData, RecurrenceType


class TestEvent:
    """Test cases for Event model."""
    
    def test_event_creation_with_required_fields(self):
        """Test successful event creation with only required fields."""
        now = datetime.now()
        event = Event(
            user_id=1,
            title="Test Event",
            start_time=now
        )
        
        assert event.user_id == 1
        assert event.title == "Test Event"
        assert event.start_time == now
        assert event.description == ""
        assert event.end_time is None
        assert event.is_all_day is False
        assert event.location == ""
        assert event.recurrence_type == RecurrenceType.NONE.name
        assert event.recurrence_data == {}
        assert event.reminder_minutes == [15]
        assert event.is_cancelled is False
    
    def test_event_creation_with_all_fields(self):
        """Test successful event creation with all fields."""
        start_time = datetime(2024, 6, 15, 14, 30)
        end_time = datetime(2024, 6, 15, 16, 0)
        recurrence_end = datetime(2024, 12, 31, 23, 59)
        last_sync = datetime.now()
        
        event = Event(
            user_id=2,
            title="Important Meeting",
            description="Quarterly review meeting",
            start_time=start_time,
            end_time=end_time,
            is_all_day=False,
            location="Conference Room A",
            recurrence_type=RecurrenceType.WEEKLY.name,
            recurrence_data={"interval": 2, "days_of_week": [1, 3]},
            recurrence_end_date=recurrence_end,
            reminder_minutes=[15, 60],
            google_calendar_id="abc123",
            is_synchronized=True,
            last_sync_time=last_sync,
            is_cancelled=False
        )
        
        assert event.user_id == 2
        assert event.title == "Important Meeting"
        assert event.description == "Quarterly review meeting"
        assert event.start_time == start_time
        assert event.end_time == end_time
        assert event.is_all_day is False
        assert event.location == "Conference Room A"
        assert event.recurrence_type == RecurrenceType.WEEKLY.name
        assert event.recurrence_data == {"interval": 2, "days_of_week": [1, 3]}
        assert event.recurrence_end_date == recurrence_end
        assert event.reminder_minutes == [15, 60]
        assert event.google_calendar_id == "abc123"
        assert event.is_synchronized is True
        assert event.last_sync_time == last_sync
        assert event.is_cancelled is False
    
    def test_event_creation_missing_user_id(self):
        """Test that event creation fails without user_id."""
        with pytest.raises(ValueError, match="user_id is required"):
            Event(title="Test", start_time=datetime.now())
    
    def test_event_creation_missing_title(self):
        """Test that event creation fails without title."""
        with pytest.raises(ValueError, match="title is required"):
            Event(user_id=1, start_time=datetime.now())
        
        with pytest.raises(ValueError, match="title is required"):
            Event(user_id=1, title="", start_time=datetime.now())
    
    def test_event_creation_missing_start_time(self):
        """Test that event creation fails without start_time."""
        with pytest.raises(ValueError, match="start_time is required"):
            Event(user_id=1, title="Test Event")
    
    def test_event_creation_none_values(self):
        """Test event creation with None values for required fields."""
        with pytest.raises(ValueError, match="user_id is required"):
            Event(user_id=None, title="Test", start_time=datetime.now())
        
        with pytest.raises(ValueError, match="start_time is required"):
            Event(user_id=1, title="Test", start_time=None)
    
    def test_event_to_dict(self):
        """Test event serialization to dictionary."""
        start_time = datetime(2024, 6, 15, 14, 30)
        end_time = datetime(2024, 6, 15, 16, 0)
        
        event = Event(
            user_id=1,
            title="Test Event",
            start_time=start_time,
            end_time=end_time,
            reminder_minutes=[10, 30]
        )
        
        event_dict = event.to_dict()
        
        assert isinstance(event_dict, dict)
        assert event_dict["user_id"] == 1
        assert event_dict["title"] == "Test Event"
        assert event_dict["start_time"] == start_time.isoformat()
        assert event_dict["end_time"] == end_time.isoformat()
        assert event_dict["reminder_minutes"] == [10, 30]
        assert "id" in event_dict
        assert "created_at" in event_dict
        assert "updated_at" in event_dict
    
    def test_event_get_next_occurrence_non_recurring(self):
        """Test next occurrence for non-recurring events."""
        future_time = datetime.now() + timedelta(days=1)
        past_time = datetime.now() - timedelta(days=1)
        
        # Future event should return its start time
        future_event = Event(
            user_id=1,
            title="Future Event",
            start_time=future_time,
            recurrence_type=RecurrenceType.NONE.name
        )
        assert future_event.get_next_occurrence() == future_time
        
        # Past event should return None
        past_event = Event(
            user_id=1,
            title="Past Event",
            start_time=past_time,
            recurrence_type=RecurrenceType.NONE.name
        )
        assert past_event.get_next_occurrence() is None
    
    def test_event_get_next_occurrence_daily(self):
        """Test next occurrence for daily recurring events."""
        start_time = datetime.now() - timedelta(days=5)
        
        event = Event(
            user_id=1,
            title="Daily Event",
            start_time=start_time,
            recurrence_type=RecurrenceType.DAILY.name,
            recurrence_data={"interval": 1}
        )
        
        next_occurrence = event.get_next_occurrence()
        assert next_occurrence is not None
        assert next_occurrence > datetime.now()
        assert next_occurrence.time() == start_time.time()
    
    def test_event_recurrence_end_date(self):
        """Test that recurrence respects end date."""
        start_time = datetime.now() - timedelta(days=10)
        end_date = datetime.now() - timedelta(days=1)  # Ended yesterday
        
        event = Event(
            user_id=1,
            title="Ended Event",
            start_time=start_time,
            recurrence_type=RecurrenceType.DAILY.name,
            recurrence_data={"interval": 1},
            recurrence_end_date=end_date
        )
        
        # Should return None since recurrence has ended
        assert event.get_next_occurrence() is None


class TestEventData:
    """Test cases for EventData Pydantic model."""
    
    def test_eventdata_creation_valid(self):
        """Test successful EventData creation with valid data."""
        start_time = datetime(2024, 6, 15, 14, 30)
        
        event_data = EventData(
            user_id=1,
            title="Test Event",
            start_time=start_time
        )
        
        assert event_data.user_id == 1
        assert event_data.title == "Test Event"
        assert event_data.start_time == start_time
        assert event_data.description == ""
        assert event_data.end_time is None
        assert event_data.is_all_day is False
        assert event_data.recurrence_type == "NONE"
        assert event_data.reminder_minutes == [15]
        assert event_data.is_cancelled is False
    
    def test_eventdata_title_validation(self):
        """Test title validation."""
        start_time = datetime.now()
        
        # Empty title should fail
        with pytest.raises(ValueError, match="title is required and cannot be empty"):
            EventData(user_id=1, title="", start_time=start_time)
        
        # Whitespace-only title should fail
        with pytest.raises(ValueError, match="title is required and cannot be empty"):
            EventData(user_id=1, title="   ", start_time=start_time)
    
    def test_eventdata_recurrence_type_validation(self):
        """Test recurrence type validation."""
        start_time = datetime.now()
        
        # Valid recurrence types
        for recurrence_type in RecurrenceType:
            event_data = EventData(
                user_id=1,
                title="Test",
                start_time=start_time,
                recurrence_type=recurrence_type.name
            )
            assert event_data.recurrence_type == recurrence_type.name
        
        # Invalid recurrence type should fail
        with pytest.raises(ValueError, match="recurrence_type must be one of"):
            EventData(
                user_id=1,
                title="Test",
                start_time=start_time,
                recurrence_type="INVALID"
            )
    
    def test_eventdata_reminder_minutes_validation(self):
        """Test reminder minutes validation."""
        start_time = datetime.now()
        
        # Valid reminder minutes
        event_data = EventData(
            user_id=1,
            title="Test",
            start_time=start_time,
            reminder_minutes=[5, 15, 60]
        )
        assert event_data.reminder_minutes == [5, 15, 60]
        
        # Negative minutes should fail
        with pytest.raises(ValueError, match="reminder_minutes must contain positive integers"):
            EventData(
                user_id=1,
                title="Test",
                start_time=start_time,
                reminder_minutes=[15, -5]
            )
    
    def test_event_from_pydantic(self):
        """Test creating Event from EventData."""
        start_time = datetime(2024, 6, 15, 14, 30)
        event_data = EventData(
            user_id=1,
            title="Test Event",
            start_time=start_time,
            description="Test description"
        )
        
        event = Event.from_pydantic(
            event_data,
            id=1,
            created_at=datetime.now()
        )
        
        assert event.user_id == 1
        assert event.title == "Test Event"
        assert event.start_time == start_time
        assert event.description == "Test description"
        assert event.id == 1
        assert event.created_at is not None


# Test runner for when running this file directly
if __name__ == "__main__":
    import traceback
    
    def run_test_method(test_class, method_name):
        """Run a single test method."""
        try:
            instance = test_class()
            method = getattr(instance, method_name)
            method()
            print(f"✓ {test_class.__name__}.{method_name}")
            return True
        except Exception as e:
            print(f"✗ {test_class.__name__}.{method_name}: {e}")
            traceback.print_exc()
            return False
    
    def run_all_tests():
        """Run all test methods."""
        test_classes = [TestEvent, TestEventData]
        total_tests = 0
        passed_tests = 0
        
        print("Running Event model tests...")
        print("=" * 50)
        
        for test_class in test_classes:
            methods = [m for m in dir(test_class) if m.startswith('test_')]
            for method_name in methods:
                total_tests += 1
                if run_test_method(test_class, method_name):
                    passed_tests += 1
        
        print("=" * 50)
        print(f"Results: {passed_tests}/{total_tests} tests passed")
        return passed_tests == total_tests
    
    if run_all_tests():
        print("All tests passed! ✓")
    else:
        print("Some tests failed! ✗")
        exit(1)
