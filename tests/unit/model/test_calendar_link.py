"""
Unit tests for CalendarLink model.

Tests both successful creation and validation failures.
"""

import pytest
from datetime import datetime

try:
    from app.model.calendar_link import Calendar<PERSON>ink, CalendarLinkData
except ImportError:
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))
    from app.model.calendar_link import CalendarLink, CalendarLinkData


class TestCalendarLink:
    """Test cases for CalendarLink model."""
    
    def test_calendar_link_creation_with_required_fields(self):
        """Test successful calendar link creation with only required fields."""
        link = CalendarLink(
            user_id=1,
            access_token="test_access_token",
            refresh_token="test_refresh_token"
        )
        
        assert link.user_id == 1
        assert link.access_token == "test_access_token"
        assert link.refresh_token == "test_refresh_token"
        assert link.token_expiry is None
        assert link.calendars == []
        assert link.default_calendar_id == ""
        assert link.is_active is True
        assert link.last_sync_time is None
        assert link.sync_frequency_minutes == 30
    
    def test_calendar_link_creation_with_all_fields(self):
        """Test successful calendar link creation with all fields."""
        now = datetime.now()
        expiry = datetime.now()
        calendars = [
            {"id": "cal1", "name": "Personal"},
            {"id": "cal2", "name": "Work"}
        ]
        
        link = CalendarLink(
            user_id=2,
            access_token="access_token_123",
            refresh_token="refresh_token_456",
            token_expiry=expiry,
            calendars=calendars,
            default_calendar_id="cal1",
            is_active=False,
            last_sync_time=now,
            sync_frequency_minutes=60
        )
        
        assert link.user_id == 2
        assert link.access_token == "access_token_123"
        assert link.refresh_token == "refresh_token_456"
        assert link.token_expiry == expiry
        assert link.calendars == calendars
        assert link.default_calendar_id == "cal1"
        assert link.is_active is False
        assert link.last_sync_time == now
        assert link.sync_frequency_minutes == 60
    
    def test_calendar_link_creation_missing_user_id(self):
        """Test that calendar link creation fails without user_id."""
        with pytest.raises(ValueError, match="user_id is required"):
            CalendarLink(
                access_token="test_token",
                refresh_token="test_refresh"
            )
    
    def test_calendar_link_creation_missing_access_token(self):
        """Test that calendar link creation fails without access_token."""
        with pytest.raises(ValueError, match="access_token is required"):
            CalendarLink(
                user_id=1,
                refresh_token="test_refresh"
            )
    
    def test_calendar_link_creation_missing_refresh_token(self):
        """Test that calendar link creation fails without refresh_token."""
        with pytest.raises(ValueError, match="refresh_token is required"):
            CalendarLink(
                user_id=1,
                access_token="test_token"
            )
    
    def test_calendar_link_creation_empty_tokens(self):
        """Test that calendar link creation fails with empty tokens."""
        with pytest.raises(ValueError, match="access_token is required"):
            CalendarLink(
                user_id=1,
                access_token="",
                refresh_token="test_refresh"
            )
        
        with pytest.raises(ValueError, match="refresh_token is required"):
            CalendarLink(
                user_id=1,
                access_token="test_token",
                refresh_token=""
            )
    
    def test_calendar_link_to_dict(self):
        """Test calendar link serialization to dictionary."""
        now = datetime.now()
        expiry = datetime.now()
        calendars = [{"id": "cal1", "name": "Test Calendar"}]
        
        link = CalendarLink(
            user_id=1,
            access_token="test_token",
            refresh_token="test_refresh",
            token_expiry=expiry,
            calendars=calendars,
            last_sync_time=now
        )
        
        link_dict = link.to_dict()
        
        assert isinstance(link_dict, dict)
        assert link_dict["user_id"] == 1
        assert link_dict["access_token"] == "test_token"
        assert link_dict["refresh_token"] == "test_refresh"
        assert link_dict["token_expiry"] == expiry.isoformat()
        assert link_dict["calendars"] == calendars
        assert link_dict["last_sync_time"] == now.isoformat()
        assert "id" in link_dict
        assert "created_at" in link_dict
        assert "updated_at" in link_dict
    
    def test_calendar_link_is_token_expired(self):
        """Test token expiry checking."""
        # Token with no expiry should be considered expired
        link = CalendarLink(
            user_id=1,
            access_token="test_token",
            refresh_token="test_refresh"
        )
        assert link.is_token_expired() is True
        
        # Token expired in the past should be expired
        past_expiry = datetime.now().replace(year=2020)
        link = CalendarLink(
            user_id=1,
            access_token="test_token",
            refresh_token="test_refresh",
            token_expiry=past_expiry
        )
        assert link.is_token_expired() is True
        
        # Token expiring in the future should not be expired
        future_expiry = datetime.now().replace(year=2030)
        link = CalendarLink(
            user_id=1,
            access_token="test_token",
            refresh_token="test_refresh",
            token_expiry=future_expiry
        )
        assert link.is_token_expired() is False


class TestCalendarLinkData:
    """Test cases for CalendarLinkData Pydantic model."""
    
    def test_calendar_link_data_creation_valid(self):
        """Test successful CalendarLinkData creation with valid data."""
        link_data = CalendarLinkData(
            user_id=1,
            access_token="test_token",
            refresh_token="test_refresh"
        )
        
        assert link_data.user_id == 1
        assert link_data.access_token == "test_token"
        assert link_data.refresh_token == "test_refresh"
        assert link_data.token_expiry is None
        assert link_data.calendars == []
        assert link_data.default_calendar_id == ""
        assert link_data.is_active is True
        assert link_data.last_sync_time is None
        assert link_data.sync_frequency_minutes == 30
    
    def test_calendar_link_data_token_validation(self):
        """Test token validation."""
        # Empty access token should fail
        with pytest.raises(ValueError, match="access_token is required and cannot be empty"):
            CalendarLinkData(
                user_id=1,
                access_token="",
                refresh_token="test_refresh"
            )
        
        # Whitespace-only access token should fail
        with pytest.raises(ValueError, match="access_token is required and cannot be empty"):
            CalendarLinkData(
                user_id=1,
                access_token="   ",
                refresh_token="test_refresh"
            )
        
        # Empty refresh token should fail
        with pytest.raises(ValueError, match="refresh_token is required and cannot be empty"):
            CalendarLinkData(
                user_id=1,
                access_token="test_token",
                refresh_token=""
            )
    
    def test_calendar_link_data_sync_frequency_validation(self):
        """Test sync frequency validation."""
        # Valid sync frequency
        link_data = CalendarLinkData(
            user_id=1,
            access_token="test_token",
            refresh_token="test_refresh",
            sync_frequency_minutes=60
        )
        assert link_data.sync_frequency_minutes == 60
        
        # Invalid sync frequency should fail
        with pytest.raises(ValueError, match="sync_frequency_minutes must be a positive integer"):
            CalendarLinkData(
                user_id=1,
                access_token="test_token",
                refresh_token="test_refresh",
                sync_frequency_minutes=0
            )
    
    def test_calendar_link_data_calendars_validation(self):
        """Test calendars validation."""
        # Valid calendars
        calendars = [
            {"id": "cal1", "name": "Personal"},
            {"id": "cal2", "name": "Work"}
        ]
        link_data = CalendarLinkData(
            user_id=1,
            access_token="test_token",
            refresh_token="test_refresh",
            calendars=calendars
        )
        assert link_data.calendars == calendars
        
        # Invalid calendar format should fail
        with pytest.raises(ValueError, match="Each calendar must have an 'id' field"):
            CalendarLinkData(
                user_id=1,
                access_token="test_token",
                refresh_token="test_refresh",
                calendars=[{"name": "Missing ID"}]
            )
    
    def test_calendar_link_from_pydantic(self):
        """Test creating CalendarLink from CalendarLinkData."""
        now = datetime.now()
        link_data = CalendarLinkData(
            user_id=1,
            access_token="test_token",
            refresh_token="test_refresh",
            sync_frequency_minutes=45
        )
        
        link = CalendarLink.from_pydantic(
            link_data,
            id=1,
            created_at=now
        )
        
        assert link.user_id == 1
        assert link.access_token == "test_token"
        assert link.refresh_token == "test_refresh"
        assert link.sync_frequency_minutes == 45
        assert link.id == 1
        assert link.created_at == now


# Test runner for when running this file directly
if __name__ == "__main__":
    import traceback
    
    def run_test_method(test_class, method_name):
        """Run a single test method."""
        try:
            instance = test_class()
            method = getattr(instance, method_name)
            method()
            print(f"✓ {test_class.__name__}.{method_name}")
            return True
        except Exception as e:
            print(f"✗ {test_class.__name__}.{method_name}: {e}")
            traceback.print_exc()
            return False
    
    def run_all_tests():
        """Run all test methods."""
        test_classes = [TestCalendarLink, TestCalendarLinkData]
        total_tests = 0
        passed_tests = 0
        
        print("Running CalendarLink model tests...")
        print("=" * 50)
        
        for test_class in test_classes:
            methods = [m for m in dir(test_class) if m.startswith('test_')]
            for method_name in methods:
                total_tests += 1
                if run_test_method(test_class, method_name):
                    passed_tests += 1
        
        print("=" * 50)
        print(f"Results: {passed_tests}/{total_tests} tests passed")
        return passed_tests == total_tests
    
    if run_all_tests():
        print("All tests passed! ✓")
    else:
        print("Some tests failed! ✗")
        exit(1)
