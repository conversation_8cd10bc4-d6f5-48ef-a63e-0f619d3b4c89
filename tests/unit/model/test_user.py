"""
Unit tests for User model.

Tests both successful creation and validation failures.
Following KISS principle with clear, focused test cases.
"""

import pytest
from datetime import datetime

# Import the models we're testing
try:
    from app.model.user import User, UserData
except ImportError:
    # Fallback for when running tests without proper environment
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))
    from app.model.user import User, UserData


class TestUser:
    """Test cases for User model."""
    
    def test_user_creation_with_required_fields(self):
        """Test successful user creation with only required fields."""
        user = User(telegram_id=12345)
        
        assert user.telegram_id == 12345
        assert user.username is None
        assert user.first_name == ""
        assert user.last_name == ""
        assert user.language_code == "en"
        assert user.is_active is True
        assert user.timezone == "UTC"
        assert user.has_google_auth is False
        assert user.last_interaction is None
    
    def test_user_creation_with_all_fields(self):
        """Test successful user creation with all fields provided."""
        now = datetime.now()
        notification_prefs = {
            "event_reminder": False,
            "daily_summary": True,
            "bot_updates": True
        }
        
        user = User(
            telegram_id=67890,
            username="testuser",
            first_name="<PERSON>",
            last_name="Doe",
            language_code="de",
            is_active=False,
            last_interaction=now,
            timezone="Europe/Berlin",
            notification_preferences=notification_prefs,
            has_google_auth=True
        )
        
        assert user.telegram_id == 67890
        assert user.username == "testuser"
        assert user.first_name == "John"
        assert user.last_name == "Doe"
        assert user.language_code == "de"
        assert user.is_active is False
        assert user.last_interaction == now
        assert user.timezone == "Europe/Berlin"
        assert user.notification_preferences == notification_prefs
        assert user.has_google_auth is True
    
    def test_user_creation_missing_telegram_id(self):
        """Test that user creation fails without telegram_id."""
        with pytest.raises(ValueError, match="telegram_id is required"):
            User()

    def test_user_creation_none_telegram_id(self):
        """Test that user creation fails with None telegram_id."""
        with pytest.raises(ValueError, match="telegram_id is required"):
            User(telegram_id=None)
    
    def test_user_creation_with_invalid_types(self):
        """Test user creation with invalid field types."""
        # These should not raise errors during creation (type checking is for static analysis)
        # But we can test the behavior
        user = User(
            telegram_id=12345,
            first_name=123,  # Wrong type but should be converted to string
            is_active="true"  # Wrong type
        )
        
        # The model should handle these gracefully
        assert user.telegram_id == 12345
        assert user.first_name == 123  # Stored as-is
        assert user.is_active == "true"  # Stored as-is
    
    def test_user_to_dict(self):
        """Test user serialization to dictionary."""
        now = datetime.now()
        user = User(
            telegram_id=12345,
            username="testuser",
            first_name="John",
            last_interaction=now
        )
        
        user_dict = user.to_dict()
        
        assert isinstance(user_dict, dict)
        assert user_dict["telegram_id"] == 12345
        assert user_dict["username"] == "testuser"
        assert user_dict["first_name"] == "John"
        assert user_dict["last_interaction"] == now.isoformat()
        assert "id" in user_dict
        assert "created_at" in user_dict
        assert "updated_at" in user_dict
    
    def test_user_to_dict_with_none_values(self):
        """Test user serialization with None values."""
        user = User(telegram_id=12345)
        user_dict = user.to_dict()
        
        assert user_dict["telegram_id"] == 12345
        assert user_dict["username"] is None
        assert user_dict["last_interaction"] is None
        assert user_dict["created_at"] is None
        assert user_dict["updated_at"] is None


class TestUserData:
    """Test cases for UserData Pydantic model."""
    
    def test_userdata_creation_valid(self):
        """Test successful UserData creation with valid data."""
        user_data = UserData(telegram_id=12345)
        
        assert user_data.telegram_id == 12345
        assert user_data.username is None
        assert user_data.first_name == ""
        assert user_data.language_code == "en"
        assert user_data.timezone == "UTC"
        assert user_data.is_active is True
        assert user_data.has_google_auth is False
    
    def test_userdata_creation_with_all_fields(self):
        """Test UserData creation with all fields."""
        now = datetime.now()
        user_data = UserData(
            telegram_id=67890,
            username="testuser",
            first_name="Jane",
            last_name="Smith",
            language_code="fr",
            is_active=False,
            last_interaction=now,
            timezone="America/New_York",
            has_google_auth=True
        )
        
        assert user_data.telegram_id == 67890
        assert user_data.username == "testuser"
        assert user_data.first_name == "Jane"
        assert user_data.last_name == "Smith"
        assert user_data.language_code == "fr"
        assert user_data.is_active is False
        assert user_data.last_interaction == now
        assert user_data.timezone == "America/New_York"
        assert user_data.has_google_auth is True
    
    def test_userdata_invalid_telegram_id_negative(self):
        """Test UserData validation fails with negative telegram_id."""
        with pytest.raises(ValueError, match="telegram_id must be a positive integer"):
            UserData(telegram_id=-1)

    def test_userdata_invalid_telegram_id_zero(self):
        """Test UserData validation fails with zero telegram_id."""
        with pytest.raises(ValueError, match="telegram_id must be a positive integer"):
            UserData(telegram_id=0)
    
    def test_userdata_language_code_validation(self):
        """Test language code validation and defaults."""
        # Empty string should default to "en"
        user_data = UserData(telegram_id=12345, language_code="")
        assert user_data.language_code == "en"
        
        # None should default to "en" (test with empty string instead)
        user_data = UserData(telegram_id=12345, language_code="")
        assert user_data.language_code == "en"
        
        # Valid code should be preserved
        user_data = UserData(telegram_id=12345, language_code="de")
        assert user_data.language_code == "de"
    
    def test_userdata_timezone_validation(self):
        """Test timezone validation and defaults."""
        # Empty string should default to "UTC"
        user_data = UserData(telegram_id=12345, timezone="")
        assert user_data.timezone == "UTC"
        
        # None should default to "UTC" (test with empty string instead)
        user_data = UserData(telegram_id=12345, timezone="")
        assert user_data.timezone == "UTC"
        
        # Valid timezone should be preserved
        user_data = UserData(telegram_id=12345, timezone="Europe/London")
        assert user_data.timezone == "Europe/London"
    
    def test_userdata_notification_preferences_default(self):
        """Test default notification preferences."""
        user_data = UserData(telegram_id=12345)
        
        expected_defaults = {
            "event_reminder": True,
            "daily_summary": False,
            "bot_updates": False
        }
        assert user_data.notification_preferences == expected_defaults
    
    def test_user_from_pydantic(self):
        """Test creating User from UserData."""
        user_data = UserData(
            telegram_id=12345,
            username="testuser",
            first_name="John"
        )
        
        user = User.from_pydantic(user_data, id=1, created_at=datetime.now())
        
        assert user.telegram_id == 12345
        assert user.username == "testuser"
        assert user.first_name == "John"
        assert user.id == 1
        assert user.created_at is not None


# Test runner for when running this file directly
if __name__ == "__main__":
    # Simple test runner without pytest
    import traceback
    
    def run_test_method(test_class, method_name):
        """Run a single test method."""
        try:
            instance = test_class()
            method = getattr(instance, method_name)
            method()
            print(f"✓ {test_class.__name__}.{method_name}")
            return True
        except Exception as e:
            print(f"✗ {test_class.__name__}.{method_name}: {e}")
            traceback.print_exc()
            return False
    
    def run_all_tests():
        """Run all test methods."""
        test_classes = [TestUser, TestUserData]
        total_tests = 0
        passed_tests = 0
        
        for test_class in test_classes:
            methods = [m for m in dir(test_class) if m.startswith('test_')]
            for method_name in methods:
                total_tests += 1
                if run_test_method(test_class, method_name):
                    passed_tests += 1
        
        print(f"\nResults: {passed_tests}/{total_tests} tests passed")
        return passed_tests == total_tests
    
    if run_all_tests():
        print("All tests passed! ✓")
    else:
        print("Some tests failed! ✗")
        exit(1)
