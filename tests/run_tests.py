#!/usr/bin/env python3
"""
Test runner for the Zeitwahl application.

This script runs all tests in the test suite. It can be used when pytest is not available
or for simple test execution during development.

Usage:
    python tests/run_tests.py [test_module]
    
Examples:
    python tests/run_tests.py                    # Run all tests
    python tests/run_tests.py user              # Run only user model tests
    python tests/run_tests.py model             # Run all model tests
"""

import sys
import traceback
import importlib.util
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def discover_test_modules(test_dir="tests/unit/model"):
    """Discover all test modules in the given directory."""
    test_modules = []
    test_path = Path(test_dir)
    
    if test_path.exists():
        for test_file in test_path.glob("test_*.py"):
            module_name = test_file.stem
            test_modules.append((module_name, test_file))
    
    return test_modules


def import_test_module(module_path):
    """Import a test module dynamically."""
    try:
        # Convert file path to module path
        module_name = module_path.stem

        spec = importlib.util.spec_from_file_location(module_name, str(module_path))
        if spec is None or spec.loader is None:
            raise ImportError(f"Could not load spec for {module_path}")

        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        return module
    except Exception as e:
        print(f"Failed to import {module_path}: {e}")
        return None


def find_test_classes(module):
    """Find all test classes in a module."""
    test_classes = []
    for name in dir(module):
        obj = getattr(module, name)
        if (isinstance(obj, type) and 
            name.startswith('Test') and 
            hasattr(obj, '__module__')):
            test_classes.append(obj)
    return test_classes


def find_test_methods(test_class):
    """Find all test methods in a test class."""
    return [method for method in dir(test_class) if method.startswith('test_')]


def run_test_method(test_class, method_name, verbose=True):
    """Run a single test method."""
    try:
        instance = test_class()
        method = getattr(instance, method_name)
        method()
        if verbose:
            print(f"✓ {test_class.__name__}.{method_name}")
        return True, None
    except Exception as e:
        error_msg = f"{test_class.__name__}.{method_name}: {e}"
        if verbose:
            print(f"✗ {error_msg}")
            traceback.print_exc()
        return False, error_msg


def run_test_class(test_class, verbose=True):
    """Run all test methods in a test class."""
    methods = find_test_methods(test_class)
    passed = 0
    failed = 0
    failures = []
    
    if verbose:
        print(f"\nRunning {test_class.__name__}:")
        print("-" * 40)
    
    for method_name in methods:
        success, error = run_test_method(test_class, method_name, verbose)
        if success:
            passed += 1
        else:
            failed += 1
            failures.append(error)
    
    return passed, failed, failures


def run_test_module(module_path, verbose=True):
    """Run all tests in a module."""
    if verbose:
        print(f"\n{'='*60}")
        print(f"Running tests from: {module_path.name}")
        print(f"{'='*60}")
    
    module = import_test_module(module_path)
    if not module:
        return 0, 1, [f"Failed to import {module_path}"]
    
    test_classes = find_test_classes(module)
    if not test_classes:
        if verbose:
            print("No test classes found in module")
        return 0, 0, []
    
    total_passed = 0
    total_failed = 0
    all_failures = []
    
    for test_class in test_classes:
        passed, failed, failures = run_test_class(test_class, verbose)
        total_passed += passed
        total_failed += failed
        all_failures.extend(failures)
    
    if verbose:
        print(f"\nModule summary: {total_passed} passed, {total_failed} failed")
    
    return total_passed, total_failed, all_failures


def run_all_tests(test_filter=None, verbose=True):
    """Run all tests, optionally filtered by name."""
    test_modules = discover_test_modules()
    
    if test_filter:
        test_modules = [
            (name, path) for name, path in test_modules 
            if test_filter.lower() in name.lower()
        ]
    
    if not test_modules:
        print("No test modules found!")
        return False
    
    total_passed = 0
    total_failed = 0
    all_failures = []
    
    for module_name, module_path in test_modules:
        passed, failed, failures = run_test_module(module_path, verbose)
        total_passed += passed
        total_failed += failed
        all_failures.extend(failures)
    
    # Print summary
    print(f"\n{'='*60}")
    print(f"FINAL RESULTS")
    print(f"{'='*60}")
    print(f"Total tests run: {total_passed + total_failed}")
    print(f"Passed: {total_passed}")
    print(f"Failed: {total_failed}")
    
    if all_failures:
        print(f"\nFailures:")
        for i, failure in enumerate(all_failures, 1):
            print(f"{i}. {failure}")
    
    success = total_failed == 0
    if success:
        print("\n🎉 All tests passed!")
    else:
        print(f"\n❌ {total_failed} test(s) failed")
    
    return success


def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Run tests for the Zeitwahl application",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python tests/run_tests.py                 # Run all tests
  python tests/run_tests.py user            # Run user model tests
  python tests/run_tests.py conversation    # Run conversation model tests
  python tests/run_tests.py --quiet         # Run with minimal output
        """
    )
    
    parser.add_argument(
        'filter', 
        nargs='?', 
        help='Filter tests by module name (e.g., "user", "event")'
    )
    parser.add_argument(
        '--quiet', '-q',
        action='store_true',
        help='Reduce output verbosity'
    )
    
    args = parser.parse_args()
    
    print("Zeitwahl Test Runner")
    print("=" * 60)
    
    if args.filter:
        print(f"Running tests matching: {args.filter}")
    else:
        print("Running all tests")
    
    success = run_all_tests(
        test_filter=args.filter,
        verbose=not args.quiet
    )
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
