"""
Bot package for Telegram bot functionality.

This package contains all the components needed to run the Telegram bot:
- Bot: Main bot class for initialization and management
- Handlers: Message and command handlers
- UserService: User identification and management service
"""

from .bot import TelegramBot
from .handlers import MessageHandler
from .user_service import UserService

__all__ = [
    'TelegramBot',
    'MessageHandler', 
    'UserService',
]
