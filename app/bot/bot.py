"""
Main Telegram bot class.

This module contains the main bot class that initializes and manages
the Telegram bot using aiogram.
"""

import asyncio
import logging
from typing import Optional

from aiogram import <PERSON><PERSON>, Dispatcher
from aiogram.client.default import DefaultBotProperties
from aiogram.enums import ParseMode

from ..config.bot_config import bot_config
from .handlers import MessageHandler


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TelegramBot:
    """Main Telegram bot class using aiogram."""
    
    def __init__(self, token: Optional[str] = None):
        """
        Initialize the Telegram bot.
        
        Args:
            token: Bot token. If not provided, will use token from config.
        """
        self.token = token or bot_config.token
        
        if not self.token:
            raise ValueError("Bot token is required. Set TELEGRAM_BOT_TOKEN environment variable.")
        
        # Validate token format
        if not bot_config.validate_token() and not self._validate_token(self.token):
            raise ValueError("Invalid bot token format.")
        
        # Initialize bot and dispatcher
        self.bot = Bot(
            token=self.token,
            default=DefaultBotProperties(parse_mode=ParseMode.HTML)
        )
        self.dp = Dispatcher()
        
        # Initialize handlers
        self.message_handler = MessageHandler()
        
        # Register handlers with dispatcher
        self._register_handlers()
        
        logger.info("Telegram bot initialized successfully")
    
    def _validate_token(self, token: str) -> bool:
        """
        Validate bot token format.
        
        Args:
            token: Bot token to validate
            
        Returns:
            True if token format is valid, False otherwise
        """
        if not token:
            return False
        
        parts = token.split(":")
        if len(parts) != 2:
            return False
            
        try:
            int(parts[0])  # First part should be numeric
            return len(parts[1]) > 10  # Second part should be reasonably long
        except ValueError:
            return False
    
    def _register_handlers(self):
        """Register all handlers with the dispatcher."""
        # Include the message handler router
        self.dp.include_router(self.message_handler.get_router())
        
        logger.info("Handlers registered with dispatcher")
    
    async def start_polling(self):
        """Start the bot with polling."""
        logger.info("Starting bot with polling...")
        
        try:
            # Start polling
            await self.dp.start_polling(
                self.bot,
                polling_timeout=bot_config.polling_timeout
            )
        except Exception as e:
            logger.error(f"Error during polling: {e}")
            raise
        finally:
            await self.bot.session.close()
    
    async def stop(self):
        """Stop the bot and clean up resources."""
        logger.info("Stopping bot...")
        
        try:
            await self.bot.session.close()
            logger.info("Bot stopped successfully")
        except Exception as e:
            logger.error(f"Error stopping bot: {e}")
    
    async def send_message(self, chat_id: int, text: str) -> bool:
        """
        Send a message to a specific chat.
        
        Args:
            chat_id: Telegram chat ID
            text: Message text to send
            
        Returns:
            True if message was sent successfully, False otherwise
        """
        try:
            await self.bot.send_message(chat_id=chat_id, text=text)
            return True
        except Exception as e:
            logger.error(f"Failed to send message to {chat_id}: {e}")
            return False


async def main():
    """Main function to run the bot."""
    try:
        bot = TelegramBot()
        await bot.start_polling()
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Bot error: {e}")


if __name__ == "__main__":
    asyncio.run(main())
