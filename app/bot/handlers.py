"""
Enhanced message handlers for the Telegram bot with event-driven architecture.

This module contains handlers for processing different types of messages
and commands received by the Telegram bot. It integrates with the event
bus system to publish events for the preprocessing pipeline.
"""

import logging
from datetime import datetime
from aiogram import Router
from aiogram.filters import Command
from aiogram.types import Message

from ..services.user_service import UserService
from ..utils.events import MessageReceived, ResponseReady, ErrorOccurred
from ..utils.event_bus import event_bus
from ..utils.constants import Platform, ErrorType

logger = logging.getLogger(__name__)


class MessageHandler:
    """Enhanced handler class for processing Telegram messages with event integration."""

    def __init__(self):
        """Initialize the message handler."""
        self.user_service = UserService()
        self.router = Router()
        self._register_handlers()
        self._register_event_handlers()

        logger.info("Message handler initialized with event integration")

    def _register_handlers(self):
        """Register all message handlers with the router."""
        # Register the message handler for all text messages
        self.router.message.register(
            self.handle_message,
            lambda message: message.text is not None
        )

        # Register start command handler
        self.router.message.register(
            self.handle_start_command,
            Command("start")
        )

        # Register help command handler
        self.router.message.register(
            self.handle_help_command,
            Command("help")
        )

    def _register_event_handlers(self):
        """Register event handlers for the event bus."""
        # Subscribe to response ready events to send messages back to users
        event_bus.subscribe(ResponseReady, self.handle_response_ready)

        # Subscribe to error events to send error messages to users
        event_bus.subscribe(ErrorOccurred, self.handle_error_occurred)
    
    async def handle_message(self, message: Message) -> None:
        """
        Handle incoming text messages.
        
        Args:
            message: The incoming Telegram message
        """
        # Identify the user from the message
        telegram_user = self.user_service.identify_user_from_message(message)
        
        if not telegram_user:
            await message.reply("Sorry, I couldn't identify you.")
            return
        
        # Create user model (in a real app, this would check if user exists first)
        user = self.user_service.create_user_from_telegram(telegram_user)
        
        # Format and send greeting message
        greeting = self.user_service.format_greeting_message(telegram_user)
        await message.reply(greeting)
    
    async def handle_start_command(self, message: Message) -> None:
        """
        Handle the /start command.
        
        Args:
            message: The incoming Telegram message with /start command
        """
        # Identify the user from the message
        telegram_user = self.user_service.identify_user_from_message(message)
        
        if not telegram_user:
            await message.reply("Sorry, I couldn't identify you.")
            return
        
        # Create user model (in a real app, this would check if user exists first)
        user = self.user_service.create_user_from_telegram(telegram_user)
        
        # Format and send greeting message
        greeting = self.user_service.format_greeting_message(telegram_user)
        welcome_message = f"{greeting}! Welcome to Zeitwahl bot. Send me any message and I'll greet you!"
        
        await message.reply(welcome_message)
    
    def get_router(self) -> Router:
        """
        Get the router with registered handlers.
        
        Returns:
            Configured aiogram Router
        """
        return self.router
