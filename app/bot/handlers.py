"""
Enhanced message handlers for the Telegram bot with event-driven architecture.

This module contains handlers for processing different types of messages
and commands received by the Telegram bot. It integrates with the event
bus system to publish events for the preprocessing pipeline.
"""

import logging
from datetime import datetime
from aiogram import Router
from aiogram.filters import Command
from aiogram.types import Message

from ..services.user_service import UserService
from ..utils.events import MessageReceived, ResponseReady, ErrorOccurred
from ..utils.event_bus import event_bus
from ..utils.constants import Platform, ErrorType

logger = logging.getLogger(__name__)


class MessageHandler:
    """Enhanced handler class for processing Telegram messages with event integration."""

    def __init__(self):
        """Initialize the message handler."""
        self.user_service = UserService()
        self.router = Router()
        self._register_handlers()
        self._register_event_handlers()

        logger.info("Message handler initialized with event integration")

    def _register_handlers(self):
        """Register all message handlers with the router."""
        # Register the message handler for all text messages
        self.router.message.register(
            self.handle_message,
            lambda message: message.text is not None
        )

        # Register start command handler
        self.router.message.register(
            self.handle_start_command,
            Command("start")
        )

        # Register help command handler
        self.router.message.register(
            self.handle_help_command,
            Command("help")
        )

    def _register_event_handlers(self):
        """Register event handlers for the event bus."""
        # Subscribe to events using the new event bus pattern
        event_bus.subscribe_tagged_methods(self)
    
    async def handle_message(self, message: Message) -> None:
        """
        Handle incoming text messages by publishing events to the processing pipeline.

        Args:
            message: The incoming Telegram message
        """
        try:
            logger.info(f"Received message from user {message.from_user.id if message.from_user else 'unknown'}")

            # Validate message
            if not message.text or not message.text.strip():
                await message.reply("Please send a non-empty message.")
                return

            if not message.from_user:
                await message.reply("Sorry, I couldn't identify you.")
                return

            # Publish message received event to start the processing pipeline
            await event_bus.publish(MessageReceived(
                user_id=message.from_user.id,
                chat_id=message.chat.id,
                message_text=message.text.strip(),
                message_id=str(message.message_id),
                timestamp=datetime.now(),
                platform=Platform.TELEGRAM.value,
                metadata={
                    'username': message.from_user.username,
                    'first_name': message.from_user.first_name,
                    'language_code': message.from_user.language_code,
                    'chat_type': message.chat.type
                }
            ))

            logger.debug(f"Published MessageReceived event for user {message.from_user.id}")

        except Exception as e:
            logger.error(f"Error handling message: {e}", exc_info=True)
            await message.reply("Sorry, I encountered an error processing your message. Please try again.")
    
    async def handle_start_command(self, message: Message) -> None:
        """
        Handle the /start command.

        Args:
            message: The incoming Telegram message with /start command
        """
        try:
            if not message.from_user:
                await message.reply("Sorry, I couldn't identify you.")
                return

            # Get user display name
            display_name = self.user_service.get_user_display_name(message.from_user)

            welcome_message = f"""Hello {display_name}! 👋

Welcome to Zeitwahl, your intelligent calendar and time management assistant!

I can help you with:
📅 Creating and managing calendar events
⏰ Setting reminders and notifications
🔍 Searching your schedule
📊 Time management and planning

Just send me a message describing what you'd like to do, and I'll take care of it!

For example, try:
• "Schedule a meeting tomorrow at 2 PM"
• "What do I have planned for this week?"
• "Set a reminder to call John at 5 PM"

Type /help for more information."""

            await message.reply(welcome_message)

            # Also publish as a regular message for processing
            await event_bus.publish(MessageReceived(
                user_id=message.from_user.id,
                chat_id=message.chat.id,
                message_text="/start",
                message_id=str(message.message_id),
                timestamp=datetime.now(),
                platform=Platform.TELEGRAM.value,
                metadata={
                    'username': message.from_user.username,
                    'first_name': message.from_user.first_name,
                    'language_code': message.from_user.language_code,
                    'chat_type': message.chat.type,
                    'is_command': True
                }
            ))

        except Exception as e:
            logger.error(f"Error handling /start command: {e}", exc_info=True)
            await message.reply("Sorry, I encountered an error. Please try again.")

    async def handle_help_command(self, message: Message) -> None:
        """
        Handle the /help command.

        Args:
            message: The incoming Telegram message with /help command
        """
        try:
            help_message = """🤖 Zeitwahl Help

**Available Commands:**
/start - Start the bot and see welcome message
/help - Show this help message

**What I can do:**
📅 **Calendar Management**
• Create events: "Schedule a meeting with John tomorrow at 3 PM"
• Search events: "What meetings do I have this week?"
• Update events: "Move my 2 PM meeting to 3 PM"

⏰ **Reminders**
• Set reminders: "Remind me to call Sarah at 5 PM"
• List reminders: "What reminders do I have?"

🔍 **Schedule Queries**
• Check availability: "Am I free tomorrow afternoon?"
• Get summaries: "What's my schedule for today?"

💡 **Tips:**
• Use natural language - I understand conversational requests
• Specify times and dates clearly
• I can work with different time zones

Just send me a message describing what you need, and I'll help you manage your time effectively!"""

            await message.reply(help_message)

        except Exception as e:
            logger.error(f"Error handling /help command: {e}", exc_info=True)
            await message.reply("Sorry, I encountered an error. Please try again.")

    @event_bus.subscribe("ResponseReady")
    async def handle_response_ready(self, event: ResponseReady) -> None:
        """
        Handle response ready events by sending messages back to users.

        Args:
            event: ResponseReady event containing the response to send
        """
        try:
            # In a complete implementation, we would need access to the bot instance
            # to send messages. For now, we'll log the response.
            logger.info(f"Response ready for user {event.user_id}: {event.response_text[:100]}...")

            # TODO: Implement actual message sending when bot instance is available
            # await bot.send_message(chat_id=event.chat_id, text=event.response_text)

        except Exception as e:
            logger.error(f"Error handling response ready event: {e}", exc_info=True)

    @event_bus.subscribe("ErrorOccurred")
    async def handle_error_occurred(self, event: ErrorOccurred) -> None:
        """
        Handle error events by sending error messages to users.

        Args:
            event: ErrorOccurred event containing error information
        """
        try:
            if not event.user_id or not event.chat_id:
                return  # Can't send message without user/chat info

            # Determine user-friendly error message
            error_message = self._get_user_friendly_error_message(event.error_type)

            logger.warning(f"Sending error message to user {event.user_id}: {error_message}")

            # TODO: Implement actual error message sending when bot instance is available
            # await bot.send_message(chat_id=event.chat_id, text=error_message)

        except Exception as e:
            logger.error(f"Error handling error occurred event: {e}", exc_info=True)

    def _get_user_friendly_error_message(self, error_type: str) -> str:
        """
        Get a user-friendly error message based on error type.

        Args:
            error_type: The type of error that occurred

        Returns:
            User-friendly error message
        """
        error_messages = {
            ErrorType.VALIDATION_ERROR.value: "Sorry, I couldn't understand your request. Please try rephrasing it.",
            ErrorType.LLM_ERROR.value: "I'm having trouble processing your request right now. Please try again in a moment.",
            ErrorType.TOOL_EXECUTION_ERROR.value: "I encountered an issue while trying to complete your request. Please try again.",
            ErrorType.NETWORK_ERROR.value: "I'm having connectivity issues. Please try again in a moment.",
            ErrorType.DATABASE_ERROR.value: "I'm having trouble accessing my data. Please try again later.",
            ErrorType.UNEXPECTED_ERROR.value: "Something unexpected happened. Please try again."
        }

        return error_messages.get(error_type, "I encountered an error. Please try again.")

    def get_router(self) -> Router:
        """
        Get the router with registered handlers.
        
        Returns:
            Configured aiogram Router
        """
        return self.router
