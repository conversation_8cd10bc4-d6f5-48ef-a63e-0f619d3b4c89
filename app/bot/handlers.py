"""
Message handlers for the Telegram bot.

This module contains handlers for processing different types of messages
and commands received by the bot.
"""

from aiogram import Router
from aiogram.types import Message
from aiogram.filters import Command

from .user_service import UserService


class MessageHandler:
    """Handler class for processing Telegram messages."""
    
    def __init__(self):
        """Initialize the message handler."""
        self.user_service = UserService()
        self.router = Router()
        self._register_handlers()
    
    def _register_handlers(self):
        """Register all message handlers with the router."""
        # Register the message handler for all text messages
        self.router.message.register(
            self.handle_message,
            lambda message: message.text is not None
        )
        
        # Register start command handler
        self.router.message.register(
            self.handle_start_command,
            Command("start")
        )
    
    async def handle_message(self, message: Message) -> None:
        """
        Handle incoming text messages.
        
        Args:
            message: The incoming Telegram message
        """
        # Identify the user from the message
        telegram_user = self.user_service.identify_user_from_message(message)
        
        if not telegram_user:
            await message.reply("Sorry, I couldn't identify you.")
            return
        
        # Create user model (in a real app, this would check if user exists first)
        user = self.user_service.create_user_from_telegram(telegram_user)
        
        # Format and send greeting message
        greeting = self.user_service.format_greeting_message(telegram_user)
        await message.reply(greeting)
    
    async def handle_start_command(self, message: Message) -> None:
        """
        Handle the /start command.
        
        Args:
            message: The incoming Telegram message with /start command
        """
        # Identify the user from the message
        telegram_user = self.user_service.identify_user_from_message(message)
        
        if not telegram_user:
            await message.reply("Sorry, I couldn't identify you.")
            return
        
        # Create user model (in a real app, this would check if user exists first)
        user = self.user_service.create_user_from_telegram(telegram_user)
        
        # Format and send greeting message
        greeting = self.user_service.format_greeting_message(telegram_user)
        welcome_message = f"{greeting}! Welcome to Zeitwahl bot. Send me any message and I'll greet you!"
        
        await message.reply(welcome_message)
    
    def get_router(self) -> Router:
        """
        Get the router with registered handlers.
        
        Returns:
            Configured aiogram Router
        """
        return self.router
