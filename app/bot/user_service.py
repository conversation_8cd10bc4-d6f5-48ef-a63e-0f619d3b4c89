"""
User service for managing bot users.

This module provides functionality to identify users from Telegram messages
and manage user data in the application.
"""

from typing import Optional
from aiogram.types import User as TelegramUser, Message as TelegramMessage
from datetime import datetime

from ..model.user import User


class UserService:
    """Service for managing bot users and user identification."""
    
    def __init__(self):
        """Initialize the user service."""
        pass
    
    def identify_user_from_message(self, message: TelegramMessage) -> Optional[TelegramUser]:
        """
        Extract user information from a Telegram message.
        
        Args:
            message: The Telegram message object
            
        Returns:
            TelegramUser object if user is found, None otherwise
        """
        if not message or not message.from_user:
            return None
            
        return message.from_user
    
    def create_user_from_telegram(self, telegram_user: TelegramUser) -> User:
        """
        Create a User model instance from Telegram user data.
        
        Args:
            telegram_user: Telegram user object
            
        Returns:
            User model instance
        """
        return User(
            telegram_id=telegram_user.id,
            username=telegram_user.username,
            first_name=telegram_user.first_name or "",
            last_name=telegram_user.last_name or "",
            language_code=telegram_user.language_code or "en",
            is_active=True,
            last_interaction=datetime.now()
        )
    
    def get_user_display_name(self, telegram_user: TelegramUser) -> str:
        """
        Get a display name for the user.
        
        Args:
            telegram_user: Telegram user object
            
        Returns:
            User's display name (first name, username, or "User")
        """
        if telegram_user.first_name:
            return telegram_user.first_name
        elif telegram_user.username:
            return telegram_user.username
        else:
            return "User"
    
    def format_greeting_message(self, telegram_user: TelegramUser) -> str:
        """
        Format a greeting message for the user.
        
        Args:
            telegram_user: Telegram user object
            
        Returns:
            Formatted greeting message
        """
        display_name = self.get_user_display_name(telegram_user)
        return f"Hello {display_name}"
