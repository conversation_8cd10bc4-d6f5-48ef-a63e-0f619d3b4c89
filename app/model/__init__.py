"""
Model package for the Zeitwahl application.

This module contains data models for the application that are persistence-ignorant:
- User: Represents a Telegram bot user
- Conversation: Represents a chat session between a user and the bot
- Message: Represents an individual message within a conversation
- Event: Represents a calendar event or reminder
- CalendarLink: Represents a connection to Google Calendar

All models use Pydantic for validation and are designed to be used with
the repository pattern for database operations.
"""

from .base_model import BaseModel
from .user import User, UserData
from .conversation import Conversation, ConversationData
from .message import Message, MessageData
from .event import Event, RecurrenceType
from .calendar_link import CalendarLink, CalendarLinkData

__all__ = [
    'BaseModel',
    'User',
    'UserData',
    'Conversation',
    'ConversationData',
    'Message',
    'MessageData',
    'Event',
    'RecurrenceType',
    'CalendarLink',
    'CalendarLinkData',
]