"""
Conversation model for storing chat sessions between users and the bot.

This module defines the Conversation model which represents a conversation
context between a user and the Telegram bot. It no longer stores individual
messages, which are now in the Message model.
"""

from datetime import datetime
from typing import Dict, Any, ClassVar, Optional

from pydantic import BaseModel as PydanticBaseModel, Field

from ..config.schema import USER_SCHEMA, CONVERSATIONS_TABLE
from .base_model import BaseModel


class ConversationData(PydanticBaseModel):
    """Pydantic model for validating Conversation data."""
    
    user_id: int
    telegram_chat_id: int
    title: str = ""
    context: Dict[str, Any] = Field(default_factory=dict)
    is_active: bool = True
    last_message_at: Optional[datetime] = None
    
    class Config:
        """Pydantic configuration."""
        arbitrary_types_allowed = True


class Conversation(BaseModel):
    """
    Conversation model representing a chat session between a user and the Telegram bot.
    
    Stores conversation context and state information to maintain
    conversation flow across multiple messages.
    """
    
    # Class variables for database configuration
    table_name: ClassVar[str] = CONVERSATIONS_TABLE
    schema_name: ClassVar[str] = USER_SCHEMA
    primary_key: ClassVar[str] = "id"
    
    def __init__(self, **kwargs):
        """
        Initialize a Conversation instance.
        
        Args:
            **kwargs: Conversation attributes including:
                user_id: Database ID of the user
                telegram_chat_id: Telegram chat ID
                title: Title or summary of the conversation
                context: Conversation context/state
                is_active: Whether the conversation is active
                last_message_at: Timestamp of the last message in this conversation
        """
        super().__init__(**kwargs)

        # Relationships - user_id is required
        user_id = kwargs.get('user_id')
        if user_id is None:
            raise ValueError("user_id is required")
        self.user_id: int = user_id

        # Telegram specific fields - telegram_chat_id is required
        telegram_chat_id = kwargs.get('telegram_chat_id')
        if telegram_chat_id is None:
            raise ValueError("telegram_chat_id is required")
        self.telegram_chat_id: int = telegram_chat_id
        
        # Conversation details
        self.title: str = kwargs.get('title', '')
        self.context: Dict[str, Any] = kwargs.get('context', {})
        self.is_active: bool = kwargs.get('is_active', True)
        self.last_message_at: Optional[datetime] = kwargs.get('last_message_at')
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the conversation model to a dictionary.
        
        Returns:
            Dictionary representation of the conversation
        """
        conv_dict = super().to_dict()
        conv_dict.update({
            'user_id': self.user_id,
            'telegram_chat_id': self.telegram_chat_id,
            'title': self.title,
            'context': self.context,
            'is_active': self.is_active,
            'last_message_at': self.last_message_at.isoformat() if self.last_message_at else None,
        })
        return conv_dict
    
    @classmethod
    def from_pydantic(cls, conversation_data: ConversationData, **extra_fields) -> 'Conversation':
        """
        Create a Conversation instance from validated pydantic data.
        
        Args:
            conversation_data: Validated ConversationData instance
            **extra_fields: Additional fields like id, created_at, etc.
            
        Returns:
            Conversation instance
        """
        data = conversation_data.model_dump()
        data.update(extra_fields)
        return cls(**data)