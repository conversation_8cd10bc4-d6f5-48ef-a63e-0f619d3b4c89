"""
Google Calendar integration model for linking user accounts with Google Calendar.

This module defines the CalendarLink model which manages the integration between
user accounts and Google Calendar, including authentication tokens and sync status.
"""

from datetime import datetime
from typing import Dict, Any, List, ClassVar, Optional

from pydantic import BaseModel as PydanticBaseModel, Field, field_validator

from ..config.schema import CALENDAR_SCHEMA, CALENDAR_LINKS_TABLE
from .base_model import BaseModel


class CalendarLinkData(PydanticBaseModel):
    """Pydantic model for validating CalendarLink data."""
    
    user_id: int
    access_token: str
    refresh_token: str
    token_expiry: Optional[datetime] = None
    calendars: List[Dict[str, Any]] = Field(default_factory=list)
    default_calendar_id: str = ""
    is_active: bool = True
    last_sync_time: Optional[datetime] = None
    sync_frequency_minutes: int = 30
    
    @field_validator('access_token')
    @classmethod
    def validate_access_token(cls, v):
        if not v or not v.strip():
            raise ValueError("access_token is required and cannot be empty")
        return v
    
    @field_validator('refresh_token')
    @classmethod
    def validate_refresh_token(cls, v):
        if not v or not v.strip():
            raise ValueError("refresh_token is required and cannot be empty")
        return v
    
    @field_validator('sync_frequency_minutes')
    @classmethod
    def validate_sync_frequency(cls, v):
        if v < 1:
            raise ValueError("sync_frequency_minutes must be a positive integer")
        return v
    
    @field_validator('calendars')
    @classmethod
    def validate_calendars(cls, v):
        for calendar in v:
            if not isinstance(calendar, dict):
                raise ValueError("Each calendar must be a dictionary")
            if 'id' not in calendar:
                raise ValueError("Each calendar must have an 'id' field")
            if 'name' not in calendar:
                raise ValueError("Each calendar must have a 'name' field")
        return v
    
    class Config:
        """Pydantic configuration."""
        arbitrary_types_allowed = True


class CalendarLink(BaseModel):
    """
    CalendarLink model representing a connection between a user and Google Calendar.
    
    Stores authentication tokens, selected calendars, and sync status to enable
    bidirectional synchronization of events between the bot and Google Calendar.
    """
    
    # Class variables for database configuration
    table_name: ClassVar[str] = CALENDAR_LINKS_TABLE
    schema_name: ClassVar[str] = CALENDAR_SCHEMA
    primary_key: ClassVar[str] = "id"
    
    def __init__(self, **kwargs):
        """
        Initialize a CalendarLink instance.
        
        Args:
            **kwargs: CalendarLink attributes including:
                user_id: Database ID of the user
                access_token: OAuth access token
                refresh_token: OAuth refresh token
                token_expiry: Timestamp when the access token expires
                calendars: List of connected Google calendars
                is_active: Whether the link is active
        """
        super().__init__(**kwargs)

        # Relationships - user_id is required
        user_id = kwargs.get('user_id')
        if user_id is None:
            raise ValueError("user_id is required")
        self.user_id: int = user_id

        # OAuth authentication data - tokens are required
        access_token = kwargs.get('access_token', '')
        if not access_token:
            raise ValueError("access_token is required")
        self.access_token: str = access_token

        refresh_token = kwargs.get('refresh_token', '')
        if not refresh_token:
            raise ValueError("refresh_token is required")
        self.refresh_token: str = refresh_token

        self.token_expiry: Optional[datetime] = kwargs.get('token_expiry')
        
        # Calendar selection and configuration
        self.calendars: List[Dict[str, Any]] = kwargs.get('calendars', [])
        self.default_calendar_id: str = kwargs.get('default_calendar_id', '')
        
        # Sync status
        self.is_active: bool = kwargs.get('is_active', True)
        self.last_sync_time: Optional[datetime] = kwargs.get('last_sync_time')
        self.sync_frequency_minutes: int = kwargs.get('sync_frequency_minutes', 30)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the calendar link model to a dictionary.
        
        Returns:
            Dictionary representation of the calendar link
        """
        link_dict = super().to_dict()
        link_dict.update({
            'user_id': self.user_id,
            'access_token': self.access_token,
            'refresh_token': self.refresh_token,
            'token_expiry': self.token_expiry.isoformat() if self.token_expiry else None,
            'calendars': self.calendars,
            'default_calendar_id': self.default_calendar_id,
            'is_active': self.is_active,
            'last_sync_time': self.last_sync_time.isoformat() if self.last_sync_time else None,
            'sync_frequency_minutes': self.sync_frequency_minutes,
        })
        return link_dict
    
    @classmethod
    def from_pydantic(cls, calendar_link_data: CalendarLinkData, **extra_fields) -> 'CalendarLink':
        """
        Create a CalendarLink instance from validated pydantic data.
        
        Args:
            calendar_link_data: Validated CalendarLinkData instance
            **extra_fields: Additional fields like id, created_at, etc.
            
        Returns:
            CalendarLink instance
        """
        data = calendar_link_data.model_dump()
        data.update(extra_fields)
        return cls(**data)
    
    def is_token_expired(self) -> bool:
        """
        Check if the access token is expired.
        
        Returns:
            True if the token is expired, False otherwise
        """
        if not self.token_expiry:
            return True
        return self.token_expiry and datetime.now() > self.token_expiry