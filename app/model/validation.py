"""
Simple validation utilities for models.

This module provides basic validation functions following the KISS principle.
Most validation is handled by Pydantic in the model classes.
"""

from datetime import datetime
from typing import Any, Optional, List, Dict, Type, TypeVar

T = TypeVar('T')


def validate_positive_integer(value: int, field_name: str) -> int:
    """Validate that an integer is positive."""
    if value <= 0:
        raise ValueError(f"{field_name} must be a positive integer")
    return value


def validate_non_empty_string(value: str, field_name: str) -> str:
    """Validate that a string is not empty."""
    if not value or not value.strip():
        raise ValueError(f"{field_name} cannot be empty")
    return value.strip()


def validate_datetime_not_past(value: datetime, field_name: str) -> datetime:
    """Validate that a datetime is not in the past."""
    if value < datetime.now():
        raise ValueError(f"{field_name} cannot be in the past")
    return value


def validate_timezone(timezone_str: str) -> str:
    """Validate timezone string (basic validation)."""
    if not timezone_str:
        return "UTC"
    # In a real implementation, you might check against a list of valid timezones
    return timezone_str


def validate_language_code(lang_code: str) -> str:
    """Validate language code (basic validation)."""
    if not lang_code:
        return "en"
    # Basic validation - should be 2-3 characters
    if len(lang_code) not in [2, 3]:
        raise ValueError("Language code must be 2-3 characters")
    return lang_code.lower()


def validate_integer(value: Any, field: str, min_value: Optional[int] = None,
                   max_value: Optional[int] = None) -> Optional[int]:
    """
    Validate an integer value.
    
    Args:
        value: Value to validate
        field: Field name for error messages
        min_value: Minimum allowed value
        max_value: Maximum allowed value
        
    Returns:
        Validated integer or None
        
    Raises:
        ValueError: If the value is not a valid integer or outside the allowed range
    """
    if value is None:
        return None
        
    if not isinstance(value, int) or isinstance(value, bool):
        raise ValueError(f"{field} must be an integer")
        
    if min_value is not None and value < min_value:
        raise ValueError(f"{field} must be at least {min_value}")
        
    if max_value is not None and value > max_value:
        raise ValueError(f"{field} must be at most {max_value}")
        
    return value


def validate_string(value: Any, field: str, min_length: Optional[int] = None,
                  max_length: Optional[int] = None) -> Optional[str]:
    """
    Validate a string value.
    
    Args:
        value: Value to validate
        field: Field name for error messages
        min_length: Minimum allowed length
        max_length: Maximum allowed length
        
    Returns:
        Validated string or None
        
    Raises:
        ValueError: If the value is not a valid string or outside the allowed length
    """
    if value is None:
        return None
        
    if not isinstance(value, str):
        raise ValueError(f"{field} must be a string")
        
    if min_length is not None and len(value) < min_length:
        raise ValueError(f"{field} must be at least {min_length} characters long")
        
    if max_length is not None and len(value) > max_length:
        raise ValueError(f"{field} must be at most {max_length} characters long")
        
    return value


def validate_boolean(value: Any, field: str) -> Optional[bool]:
    """
    Validate a boolean value.
    
    Args:
        value: Value to validate
        field: Field name for error messages
        
    Returns:
        Validated boolean or None
        
    Raises:
        ValueError: If the value is not a valid boolean
    """
    if value is None:
        return None
        
    if not isinstance(value, bool):
        raise ValueError(f"{field} must be a boolean")
        
    return value


def validate_list(value: Any, field: str, item_type: Optional[Type[T]] = None,
                min_length: Optional[int] = None, max_length: Optional[int] = None) -> Optional[List[Any]]:
    """
    Validate a list value.
    
    Args:
        value: Value to validate
        field: Field name for error messages
        item_type: Expected type of list items
        min_length: Minimum allowed length
        max_length: Maximum allowed length
        
    Returns:
        Validated list or None
        
    Raises:
        ValueError: If the value is not a valid list or contains invalid items
    """
    if value is None:
        return None
        
    if not isinstance(value, list):
        raise ValueError(f"{field} must be a list")
        
    if min_length is not None and len(value) < min_length:
        raise ValueError(f"{field} must contain at least {min_length} items")
        
    if max_length is not None and len(value) > max_length:
        raise ValueError(f"{field} must contain at most {max_length} items")
        
    if item_type is not None:
        for i, item in enumerate(value):
            if not isinstance(item, item_type):
                raise ValueError(f"Item {i} in {field} must be a {item_type.__name__}")
                
    return value


def validate_dict(value: Any, field: str, required_keys: Optional[List[str]] = None) -> Optional[Dict[str, Any]]:
    """
    Validate a dictionary value.
    
    Args:
        value: Value to validate
        field: Field name for error messages
        required_keys: List of keys that must be present
        
    Returns:
        Validated dictionary or None
        
    Raises:
        ValueError: If the value is not a valid dictionary or missing required keys
    """
    if value is None:
        return None
        
    if not isinstance(value, dict):
        raise ValueError(f"{field} must be a dictionary")
        
    if required_keys:
        for key in required_keys:
            if key not in value:
                raise ValueError(f"Required key '{key}' missing from {field}")
                
    return value


def validate_email(value: Any, field: str) -> Optional[str]:
    """
    Validate an email address.
    
    Args:
        value: Value to validate
        field: Field name for error messages
        
    Returns:
        Validated email string or None
        
    Raises:
        ValueError: If the value is not a valid email address
    """
    import re
    
    if value is None:
        return None
        
    value = validate_string(value, field)
    
    # Basic email validation pattern
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(pattern, value):
        raise ValueError(f"{field} must be a valid email address")
        
    return value


def validate_enum(value: Any, enum_class: Type[Any], field: str) -> Any:
    """
    Validate that a value is a valid enum member.
    
    Args:
        value: Value to validate
        enum_class: Enum class to validate against
        field: Field name for error messages
        
    Returns:
        Validated enum member
        
    Raises:
        ValueError: If the value is not a valid enum member
    """
    if value is None:
        return None
        
    try:
        if isinstance(value, str):
            return enum_class[value]
        elif isinstance(value, enum_class):
            return value
        else:
            raise ValueError
    except (KeyError, ValueError):
        valid_values = [e.name for e in enum_class]
        raise ValueError(f"{field} must be one of: {', '.join(valid_values)}")