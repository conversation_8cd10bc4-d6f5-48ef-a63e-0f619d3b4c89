"""
Message model for storing individual messages in conversations.

This module defines the Message model for storing individual messages
between users and the Telegram bot within the context of a conversation.
"""

from datetime import datetime
from typing import Dict, Any, ClassVar, Optional

from pydantic import BaseModel as PydanticBaseModel, Field, field_validator

from ..config.schema import USER_SCHEMA, MESSAGES_TABLE
from .base_model import BaseModel


class MessageData(PydanticBaseModel):
    """Pydantic model for validating Message data."""
    
    conversation_id: int
    telegram_message_id: Optional[int] = None
    direction: str
    message_type: str
    content: str = ""
    metadata: Dict[str, Any] = Field(default_factory=dict)
    sent_at: Optional[datetime] = None
    is_processed: bool = False
    
    @field_validator('direction')
    @classmethod
    def validate_direction(cls, v):
        valid_directions = ["incoming", "outgoing"]
        if v not in valid_directions:
            raise ValueError(f"direction must be one of: {', '.join(valid_directions)}")
        return v
    
    @field_validator('message_type')
    @classmethod
    def validate_message_type(cls, v):
        valid_types = ["text", "command", "media", "callback", "location", "contact"]
        if v not in valid_types:
            raise ValueError(f"message_type must be one of: {', '.join(valid_types)}")
        return v
    
    class Config:
        """Pydantic configuration."""
        arbitrary_types_allowed = True


class Message(BaseModel):
    """
    Message model representing an individual message in a conversation.
    
    Stores details about a single message exchanged between a user and the bot,
    including the content, direction, and metadata.
    """
    
    # Class variables for database configuration
    table_name: ClassVar[str] = MESSAGES_TABLE
    schema_name: ClassVar[str] = USER_SCHEMA
    primary_key: ClassVar[str] = "id"
    
    # Message direction constants
    DIRECTION_INCOMING = "incoming"
    DIRECTION_OUTGOING = "outgoing"
    
    # Message type constants
    TYPE_TEXT = "text"
    TYPE_COMMAND = "command"
    TYPE_MEDIA = "media"
    TYPE_CALLBACK = "callback"
    TYPE_LOCATION = "location"
    TYPE_CONTACT = "contact"
    
    def __init__(self, **kwargs):
        """
        Initialize a Message instance.
        
        Args:
            **kwargs: Message attributes including:
                conversation_id: ID of the conversation this message belongs to
                telegram_message_id: Telegram message ID
                direction: Message direction (incoming/outgoing)
                message_type: Type of message
                content: Message content
                metadata: Additional message metadata
                sent_at: When the message was sent
                is_processed: Whether the message has been processed
        """
        super().__init__(**kwargs)

        # Relationships - conversation_id is required
        conversation_id = kwargs.get('conversation_id')
        if conversation_id is None:
            raise ValueError("conversation_id is required")
        self.conversation_id: int = conversation_id

        # Telegram specific fields
        self.telegram_message_id: Optional[int] = kwargs.get('telegram_message_id')
        
        # Message details
        self.direction: str = kwargs.get('direction', self.DIRECTION_INCOMING)
        self.message_type: str = kwargs.get('message_type', self.TYPE_TEXT)
        self.content: str = kwargs.get('content', '')
        
        # Metadata and processing info
        self.metadata: Dict[str, Any] = kwargs.get('metadata', {})
        self.sent_at: Optional[datetime] = kwargs.get('sent_at')
        self.is_processed: bool = kwargs.get('is_processed', False)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the message model to a dictionary.
        
        Returns:
            Dictionary representation of the message
        """
        msg_dict = super().to_dict()
        msg_dict.update({
            'conversation_id': self.conversation_id,
            'telegram_message_id': self.telegram_message_id,
            'direction': self.direction,
            'message_type': self.message_type,
            'content': self.content,
            'metadata': self.metadata,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None,
            'is_processed': self.is_processed
        })
        return msg_dict
    
    @classmethod
    def from_pydantic(cls, message_data: MessageData, **extra_fields) -> 'Message':
        """
        Create a Message instance from validated pydantic data.
        
        Args:
            message_data: Validated MessageData instance
            **extra_fields: Additional fields like id, created_at, etc.
            
        Returns:
            Message instance
        """
        data = message_data.model_dump()
        data.update(extra_fields)
        return cls(**data)