"""
Event model for storing user calendar events and reminders.

This module defines the Event model for storing user calendar events, reminders,
and recurring event patterns. It supports integration with Google Calendar.
"""

from datetime import datetime, timedelta, date
from typing import Dict, Any, List, ClassVar, Optional, Union
from enum import Enum, auto

from pydantic import BaseModel as PydanticBaseModel, Field, field_validator

from ..config.schema import CALENDAR_SCHEMA, EVENTS_TABLE
from .base_model import BaseModel


class RecurrenceType(Enum):
    """Enumeration of supported recurrence types for events."""
    NONE = auto()
    DAILY = auto()
    WEEKLY = auto()
    MONTHLY = auto()
    YEARLY = auto()


class EventData(PydanticBaseModel):
    """Pydantic model for validating Event data."""
    
    user_id: int
    title: str
    description: str = ""
    start_time: datetime
    end_time: Optional[datetime] = None
    is_all_day: bool = False
    location: str = ""
    recurrence_type: str = "NONE"
    recurrence_data: Dict[str, Any] = Field(default_factory=dict)
    recurrence_end_date: Optional[datetime] = None
    reminder_minutes: List[int] = Field(default_factory=lambda: [15])
    google_calendar_id: Optional[str] = None
    is_synchronized: bool = False
    last_sync_time: Optional[datetime] = None
    is_cancelled: bool = False
    
    @field_validator('title')
    @classmethod
    def validate_title(cls, v):
        if not v or not v.strip():
            raise ValueError("title is required and cannot be empty")
        return v
    
    @field_validator('end_time')
    @classmethod
    def validate_end_time(cls, v, info):
        if v is None and hasattr(info, 'data') and 'start_time' in info.data:
            # Default end_time to start_time + 1 hour
            return info.data['start_time'] + timedelta(hours=1)
        if v is not None and hasattr(info, 'data') and 'start_time' in info.data and v < info.data['start_time']:
            raise ValueError("end_time must be after start_time")
        return v
    
    @field_validator('recurrence_type')
    @classmethod
    def validate_recurrence_type(cls, v):
        try:
            RecurrenceType[v]
            return v
        except KeyError:
            valid_types = [t.name for t in RecurrenceType]
            raise ValueError(f"recurrence_type must be one of: {', '.join(valid_types)}")
    
    @field_validator('reminder_minutes')
    @classmethod
    def validate_reminder_minutes(cls, v):
        if not all(isinstance(minutes, int) and minutes >= 0 for minutes in v):
            raise ValueError("reminder_minutes must contain positive integers")
        return v
    
    class Config:
        """Pydantic configuration."""
        arbitrary_types_allowed = True


class Event(BaseModel):
    """
    Event model representing a calendar event or reminder.
    
    Stores event details including title, description, date/time information,
    recurrence patterns, and Google Calendar integration data.
    """
    
    # Class variables for database configuration
    table_name: ClassVar[str] = EVENTS_TABLE
    schema_name: ClassVar[str] = CALENDAR_SCHEMA
    primary_key: ClassVar[str] = "id"
    
    def __init__(self, **kwargs):
        """
        Initialize an Event instance.
        
        Args:
            **kwargs: Event attributes including:
                user_id: Database ID of the user who owns this event
                title: Event title
                description: Event description
                start_time: Event start time
                end_time: Event end time
                is_all_day: Whether the event is an all-day event
                location: Event location
                recurrence_type: Type of recurrence pattern
                recurrence_data: Additional recurrence configuration
                google_calendar_id: Google Calendar event ID if linked
        """
        super().__init__(**kwargs)

        # Relationships - user_id is required
        user_id = kwargs.get('user_id')
        if user_id is None:
            raise ValueError("user_id is required")
        self.user_id: int = user_id

        # Basic event details - title and start_time are required
        title = kwargs.get('title', '')
        if not title:
            raise ValueError("title is required")
        self.title: str = title

        start_time = kwargs.get('start_time')
        if start_time is None:
            raise ValueError("start_time is required")
        self.start_time: datetime = start_time

        self.description: str = kwargs.get('description', '')
        self.end_time: Optional[datetime] = kwargs.get('end_time')
        self.is_all_day: bool = kwargs.get('is_all_day', False)
        self.location: str = kwargs.get('location', '')
        
        # Recurrence information
        self.recurrence_type: str = kwargs.get('recurrence_type', RecurrenceType.NONE.name)
        self.recurrence_data: Dict[str, Any] = kwargs.get('recurrence_data', {})
        self.recurrence_end_date: Optional[datetime] = kwargs.get('recurrence_end_date')

        # Reminder settings
        self.reminder_minutes: List[int] = kwargs.get('reminder_minutes', [15])

        # Google Calendar integration
        self.google_calendar_id: Optional[str] = kwargs.get('google_calendar_id')
        self.is_synchronized: bool = kwargs.get('is_synchronized', False)
        self.last_sync_time: Optional[datetime] = kwargs.get('last_sync_time')
        
        # Status
        self.is_cancelled: bool = kwargs.get('is_cancelled', False)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the event model to a dictionary.
        
        Returns:
            Dictionary representation of the event
        """
        event_dict = super().to_dict()
        event_dict.update({
            'user_id': self.user_id,
            'title': self.title,
            'description': self.description,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'is_all_day': self.is_all_day,
            'location': self.location,
            'recurrence_type': self.recurrence_type,
            'recurrence_data': self.recurrence_data,
            'recurrence_end_date': self.recurrence_end_date.isoformat() if self.recurrence_end_date else None,
            'reminder_minutes': self.reminder_minutes,
            'google_calendar_id': self.google_calendar_id,
            'is_synchronized': self.is_synchronized,
            'last_sync_time': self.last_sync_time.isoformat() if self.last_sync_time else None,
            'is_cancelled': self.is_cancelled,
        })
        return event_dict
    
    @classmethod
    def from_pydantic(cls, event_data: EventData, **extra_fields) -> 'Event':
        """
        Create an Event instance from validated pydantic data.
        
        Args:
            event_data: Validated EventData instance
            **extra_fields: Additional fields like id, created_at, etc.
            
        Returns:
            Event instance
        """
        data = event_data.model_dump()
        data.update(extra_fields)
        return cls(**data)
    
    def get_next_occurrence(self, after_date: Optional[datetime] = None) -> Optional[datetime]:
        """
        Calculate the next occurrence of this event after the given date.
        For non-recurring events, returns None if the event is in the past.
        
        Args:
            after_date: Date to calculate the next occurrence after. Defaults to now.
            
        Returns:
            The datetime of the next occurrence, or None if no future occurrences
        """
        if after_date is None:
            after_date = datetime.now()
            
        # For non-recurring events, just return the start time if it's in the future
        if self.recurrence_type == RecurrenceType.NONE.name:
            return self.start_time if self.start_time > after_date else None
            
        # Handle recurrence patterns
        recurrence_type = RecurrenceType[self.recurrence_type]
        
        # Check if recurrence has ended
        if self.recurrence_end_date and self.recurrence_end_date < after_date:
            return None
            
        # Implementation of recurrence calculation for different patterns
        if recurrence_type == RecurrenceType.DAILY:
            return self._calculate_daily_occurrence(after_date)
        elif recurrence_type == RecurrenceType.WEEKLY:
            return self._calculate_weekly_occurrence(after_date)
        elif recurrence_type == RecurrenceType.MONTHLY:
            return self._calculate_monthly_occurrence(after_date)
        elif recurrence_type == RecurrenceType.YEARLY:
            return self._calculate_yearly_occurrence(after_date)
            
        return None
    
    # Helper methods for calculating occurrences
    def _calculate_daily_occurrence(self, after_date: datetime) -> datetime:
        """Calculate next occurrence for daily events."""
        days = self.recurrence_data.get('interval', 1)
        
        # Calculate days since the start date
        delta_days = (after_date.date() - self.start_time.date()).days
        
        # Calculate days needed to reach the next occurrence
        if delta_days % days == 0 and after_date.time() <= self.start_time.time():
            # Today is a valid occurrence day and the time hasn't passed yet
            next_date = after_date.date()
        else:
            # Calculate the next occurrence day
            days_to_add = days - (delta_days % days) if delta_days % days != 0 else days
            next_date = after_date.date() + timedelta(days=days_to_add)
            
        # Combine with the original time
        return datetime.combine(next_date, self.start_time.time())
    
    def _calculate_weekly_occurrence(self, after_date: datetime) -> datetime:
        """Calculate next occurrence for weekly events."""
        # Implementation left unchanged from original
        days_of_week = self.recurrence_data.get('days_of_week', [self.start_time.weekday()])
        weeks_interval = self.recurrence_data.get('interval', 1)
        
        # Sort the days of the week to find the next available one
        days_of_week = sorted(days_of_week)
        
        # Calculate the week number since the start date
        start_week = self.start_time.isocalendar()[1]
        current_week = after_date.isocalendar()[1]
        week_diff = (current_week - start_week) % 52
        
        # Check if the current week is a valid occurrence week
        is_valid_week = week_diff % weeks_interval == 0
        
        # Find the next valid day
        current_weekday = after_date.weekday()
        
        # First check if there's a valid day this week
        if is_valid_week:
            for day in days_of_week:
                if day > current_weekday or (day == current_weekday and after_date.time() <= self.start_time.time()):
                    days_to_add = day - current_weekday
                    next_date = after_date.date() + timedelta(days=days_to_add)
                    return datetime.combine(next_date, self.start_time.time())
        
        # If not, find the next valid week and day
        weeks_to_add = weeks_interval - (week_diff % weeks_interval) if week_diff % weeks_interval != 0 else weeks_interval
        next_week_date = after_date.date() + timedelta(weeks=weeks_to_add)
        days_to_add = days_of_week[0] - next_week_date.weekday()
        if days_to_add < 0:
            days_to_add += 7
        
        next_date = next_week_date + timedelta(days=days_to_add)
        return datetime.combine(next_date, self.start_time.time())
    
    def _calculate_monthly_occurrence(self, after_date: datetime) -> datetime:
        """Calculate next occurrence for monthly events."""
        # Implementation left unchanged from original
        months_interval = self.recurrence_data.get('interval', 1)
        
        # Handle "day of month" or "nth weekday" patterns
        if 'day_of_month' in self.recurrence_data:
            return self._calculate_monthly_by_day(after_date, months_interval)
        elif 'week_of_month' in self.recurrence_data and 'day_of_week' in self.recurrence_data:
            return self._calculate_monthly_by_weekday(after_date, months_interval)
        
        # Default: same day of month as the start date
        return self._calculate_monthly_by_day(after_date, months_interval, self.start_time.day)
    
    def _calculate_monthly_by_day(self, after_date: datetime, months_interval: int,
                                 day_of_month: Optional[Union[int, str]] = None) -> datetime:
        """Calculate next monthly occurrence by day of month."""
        # Implementation left unchanged from original
        if day_of_month is None:
            day_of_month = self.recurrence_data.get('day_of_month', self.start_time.day)

        # Ensure day_of_month is not None after assignment
        if day_of_month is None:
            day_of_month = self.start_time.day
            
        # Calculate months since the start date
        months_since_start = (after_date.year - self.start_time.year) * 12 + (after_date.month - self.start_time.month)
        
        # Determine if current month is valid and the day hasn't passed yet
        is_valid_month = months_since_start % months_interval == 0
        
        current_year = after_date.year
        current_month = after_date.month
        
        # Handle the case where we're in a valid month but need to check if the day has passed
        if is_valid_month:
            # Calculate the actual day, handling "last" day of month
            if day_of_month == "last":
                if current_month == 12:
                    next_month = 1
                    next_year = current_year + 1
                else:
                    next_month = current_month + 1
                    next_year = current_year
                last_day = (datetime(next_year, next_month, 1) - timedelta(days=1)).day
                actual_day = last_day
            else:
                # Ensure we don't exceed the days in the month
                import calendar
                if isinstance(day_of_month, str):
                    # Handle string values like "last" - fallback to start_time day
                    actual_day = self.start_time.day
                else:
                    actual_day = min(day_of_month, calendar.monthrange(current_year, current_month)[1])
            
            # Check if the day is still in the future this month
            target_date = datetime(current_year, current_month, actual_day)
            if target_date > after_date or (target_date.date() == after_date.date() and self.start_time.time() > after_date.time()):
                return datetime.combine(target_date.date(), self.start_time.time())
        
        # Calculate the next valid month
        months_to_add = months_interval - (months_since_start % months_interval) if months_since_start % months_interval != 0 else months_interval
        
        # Calculate the next date
        next_month = current_month + months_to_add
        next_year = current_year + (next_month - 1) // 12
        next_month = ((next_month - 1) % 12) + 1
        
        # Handle "last" day of month
        if day_of_month == "last":
            if next_month == 12:
                temp_month = 1
                temp_year = next_year + 1
            else:
                temp_month = next_month + 1
                temp_year = next_year
            actual_day = (datetime(temp_year, temp_month, 1) - timedelta(days=1)).day
        else:
            # Ensure we don't exceed the days in the month
            import calendar
            if isinstance(day_of_month, str):
                # Handle string values like "last" - fallback to start_time day
                actual_day = self.start_time.day
            else:
                actual_day = min(day_of_month, calendar.monthrange(next_year, next_month)[1])
        
        next_date = datetime(next_year, next_month, actual_day)
        return datetime.combine(next_date.date(), self.start_time.time())
    
    def _calculate_monthly_by_weekday(self, after_date: datetime, months_interval: int) -> datetime:
        """Calculate next monthly occurrence by week number and weekday."""
        # Implementation left unchanged from original
        week_of_month = self.recurrence_data.get('week_of_month', 1)
        day_of_week = self.recurrence_data.get('day_of_week', 0)
        
        # Calculate months since the start date
        months_since_start = (after_date.year - self.start_time.year) * 12 + (after_date.month - self.start_time.month)
        
        # Determine the month to look at
        if months_since_start % months_interval == 0:
            # Check if the occurrence in this month has already passed
            current_occurrence = self._find_weekday_in_month(after_date.year, after_date.month, 
                                                            day_of_week, week_of_month)
            if current_occurrence > after_date.date():
                return datetime.combine(current_occurrence, self.start_time.time())
        
        # Calculate the next valid month
        months_to_add = months_interval - (months_since_start % months_interval) if months_since_start % months_interval != 0 else months_interval
        
        # Calculate the next date
        next_month = after_date.month + months_to_add
        next_year = after_date.year + (next_month - 1) // 12
        next_month = ((next_month - 1) % 12) + 1
        
        next_date = self._find_weekday_in_month(next_year, next_month, day_of_week, week_of_month)
        return datetime.combine(next_date, self.start_time.time())
    
    def _find_weekday_in_month(self, year: int, month: int, day_of_week: int, week_of_month: int) -> date:
        """Find a specific weekday in a month (e.g., the second Tuesday)."""
        # Implementation left unchanged from original
        import calendar
        
        # Handle special case for 'last' week
        if week_of_month == -1:
            # Find the last occurrence of the weekday in the month
            cal = calendar.monthcalendar(year, month)
            weekdays = [week[day_of_week] for week in cal if week[day_of_week] != 0]
            return date(year, month, weekdays[-1])
        
        # Find the nth occurrence of the weekday
        cal = calendar.monthcalendar(year, month)
        weekdays = [week[day_of_week] for week in cal if week[day_of_week] != 0]
        
        # Ensure we don't exceed the available occurrences
        if week_of_month > len(weekdays):
            # If requested occurrence doesn't exist, return the last one
            return date(year, month, weekdays[-1])

        return date(year, month, weekdays[week_of_month - 1])
    
    def _calculate_yearly_occurrence(self, after_date: datetime) -> datetime:
        """Calculate next occurrence for yearly events."""
        # Implementation left unchanged from original
        years_interval = self.recurrence_data.get('interval', 1)
        
        # Calculate years since the start date
        years_since_start = after_date.year - self.start_time.year
        
        # Check if current year is a valid occurrence year
        is_valid_year = years_since_start % years_interval == 0
        
        if is_valid_year:
            # Check if the occurrence this year is still in the future
            this_year_date = datetime(
                after_date.year,
                self.start_time.month,
                min(self.start_time.day, self._days_in_month(after_date.year, self.start_time.month))
            )
            
            if this_year_date > after_date or (this_year_date.date() == after_date.date() 
                                             and self.start_time.time() > after_date.time()):
                return datetime.combine(this_year_date.date(), self.start_time.time())
        
        # Calculate next valid year
        years_to_add = years_interval - (years_since_start % years_interval) if years_since_start % years_interval != 0 else years_interval
        next_year = after_date.year + years_to_add
        
        # Handle leap years for February 29
        day = self.start_time.day
        month = self.start_time.month
        if month == 2 and day == 29 and not self._is_leap_year(next_year):
            day = 28
            
        next_date = datetime(next_year, month, day)
        return datetime.combine(next_date.date(), self.start_time.time())
    
    def _days_in_month(self, year: int, month: int) -> int:
        """Get the number of days in a month."""
        import calendar
        return calendar.monthrange(year, month)[1]
    
    def _is_leap_year(self, year: int) -> bool:
        """Check if a year is a leap year."""
        return (year % 4 == 0 and year % 100 != 0) or (year % 400 == 0)