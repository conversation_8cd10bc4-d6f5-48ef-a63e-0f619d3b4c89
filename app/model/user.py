"""
User model for the application.

This module defines the User model for storing user information related to the Telegram bot.
It includes fields for user identity, authentication, and preferences.
"""

from datetime import datetime
from typing import Dict, ClassVar, Optional

from pydantic import BaseModel as PydanticBaseModel, field_validator, Field

from ..config.schema import USER_SCHEMA, USERS_TABLE
from .base_model import BaseModel


class UserData(PydanticBaseModel):
    """Pydantic model for validating User data."""
    
    telegram_id: int
    username: Optional[str] = None
    first_name: str = ""
    last_name: str = ""
    language_code: str = "en"
    is_active: bool = True
    last_interaction: Optional[datetime] = None
    timezone: str = "UTC"
    notification_preferences: Dict[str, bool] = Field(
        default_factory=lambda: {
            "event_reminder": True,
            "daily_summary": False,
            "bot_updates": False
        }
    )
    has_google_auth: bool = False
    
    @field_validator('telegram_id')
    @classmethod
    def validate_telegram_id(cls, v):
        if v <= 0:
            raise ValueError("telegram_id must be a positive integer")
        return v
    
    @field_validator('language_code')
    @classmethod
    def validate_language_code(cls, v):
        if not v:
            return "en"
        return v
    
    @field_validator('timezone')
    @classmethod
    def validate_timezone(cls, v):
        # A more comprehensive validation would check against a list of valid timezones
        if not v:
            return "UTC"
        return v
    
    class Config:
        """Pydantic configuration."""
        arbitrary_types_allowed = True


class User(BaseModel):
    """
    User model representing a Telegram bot user.
    
    Stores essential user information including Telegram user ID, username,
    preferences, and authentication status.
    """
    
    # Class variables for database configuration
    table_name: ClassVar[str] = USERS_TABLE
    schema_name: ClassVar[str] = USER_SCHEMA
    primary_key: ClassVar[str] = "id"
    
    def __init__(self, **kwargs):
        """
        Initialize a User instance.
        
        Args:
            **kwargs: User attributes including:
                telegram_id: Telegram user ID
                username: Telegram username
                first_name: User's first name
                last_name: User's last name
                language_code: User's language preference
                is_active: Whether the user is active
        """
        super().__init__(**kwargs)

        # Telegram specific fields - telegram_id is required
        telegram_id = kwargs.get('telegram_id')
        if telegram_id is None:
            raise ValueError("telegram_id is required")
        self.telegram_id: int = telegram_id
        self.username: Optional[str] = kwargs.get('username')
        self.first_name: str = kwargs.get('first_name', '')
        self.last_name: str = kwargs.get('last_name', '')
        self.language_code: str = kwargs.get('language_code', 'en')
        
        # User status
        self.is_active: bool = kwargs.get('is_active', True)
        self.last_interaction: Optional[datetime] = kwargs.get('last_interaction')
        
        # User preferences
        self.timezone: str = kwargs.get('timezone', 'UTC')
        self.notification_preferences: Dict[str, bool] = kwargs.get('notification_preferences', {
            'event_reminder': True,
            'daily_summary': False,
            'bot_updates': False
        })
        
        # Authentication status for external services
        self.has_google_auth: bool = kwargs.get('has_google_auth', False)
    
    def to_dict(self) -> Dict:
        """
        Convert the user model to a dictionary.
        
        Returns:
            Dictionary representation of the user
        """
        user_dict = super().to_dict()
        user_dict.update({
            'telegram_id': self.telegram_id,
            'username': self.username,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'language_code': self.language_code,
            'is_active': self.is_active,
            'last_interaction': self.last_interaction.isoformat() if self.last_interaction else None,
            'timezone': self.timezone,
            'notification_preferences': self.notification_preferences,
            'has_google_auth': self.has_google_auth,
        })
        return user_dict
    
    @classmethod
    def from_pydantic(cls, user_data: UserData, **extra_fields) -> 'User':
        """
        Create a User instance from validated pydantic data.
        
        Args:
            user_data: Validated UserData instance
            **extra_fields: Additional fields like id, created_at, etc.
            
        Returns:
            User instance
        """
        data = user_data.model_dump()
        data.update(extra_fields)
        return cls(**data)