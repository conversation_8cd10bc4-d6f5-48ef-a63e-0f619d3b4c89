"""
Base model class for all models in the application.

This module provides the BaseModel class that all models should inherit from.
It defines common functionality for serialization and providing model metadata.
"""

from datetime import datetime
from typing import Any, Dict, ClassVar


class BaseModel:
    """
    Base model class for all domain models.
    
    Provides common functionality for serialization and model metadata.
    All model classes should inherit from this class.
    """
    
    # Class variables to be overridden by subclasses
    table_name: ClassVar[str] = ""
    schema_name: ClassVar[str] = "public"
    primary_key: ClassVar[str] = "id"
    
    def __init__(self, **kwargs):
        """
        Initialize a model instance with the given attributes.

        Args:
            **kwargs: Attributes to set on the model instance.
        """
        self.id: int | None = kwargs.get('id', None)
        self.created_at: datetime | None = kwargs.get('created_at', None)
        self.updated_at: datetime | None = kwargs.get('updated_at', None)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the model to a dictionary.
        
        Returns:
            Dictionary representation of the model.
        """
        return {
            'id': self.id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def table_qualified_name(cls) -> str:
        """
        Get the fully qualified table name (schema.table).
        
        Returns:
            Fully qualified table name.
        """
        return f"{cls.schema_name}.{cls.table_name}"