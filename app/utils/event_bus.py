"""
Enhanced Event Bus implementation with decorator support for decoupled communication.

This module provides a lightweight event bus system that allows different
components of the AI agent to communicate without direct dependencies.
Components can publish events and subscribe to events they're interested in.

The event bus supports:
- Decorator-based event subscription
- Tagged method auto-discovery and subscription
- Asynchronous event publishing and handling
- Type-safe event definitions
- Multiple subscribers per event type
- Priority-based subscription ordering
- Error isolation between handlers
- Event registry for debugging and monitoring

Usage:
    # Using decorators
    class MyHandler:
        @event_bus.subscribe("message_received")
        async def handle_message(self, event):
            # Handle the event
            pass

    # Register all tagged methods
    handler = MyHandler()
    event_bus.subscribe_tagged_methods(handler)

    # Publish events
    await event_bus.publish("message_received", event_data)
"""

import asyncio
import logging
import traceback
import typing
from collections import defaultdict
from functools import wraps
from typing import Any, Callable, Dict, List, Optional, Union
from dataclasses import dataclass
from abc import ABC

logger = logging.getLogger(__name__)


@dataclass
class BaseEvent(ABC):
    """Base class for all events in the system."""
    pass


class EventBus:
    """
    Enhanced event bus for event-driven communication among components.

    Supports decorator-based subscriptions, tagged method auto-discovery,
    priority-based ordering, and comprehensive event registry.
    """

    def __init__(self):
        """Initialize the EventBus."""
        self.listeners = defaultdict(list)
        self.registry = []
        self._middleware: List[Callable] = []

    def subscribe(self, topic: Union[str, type]) -> typing.Callable:
        """
        Decorator to subscribe a method to a specific event topic.

        Args:
            topic: The event topic to subscribe to (string or event class)

        Returns:
            callable: The decorator function
        """
        def decorator(function):
            @wraps(function)
            async def wrapper(self, *args, **kwargs):
                return await function(self, *args, **kwargs)

            # Convert event class to string if needed
            topic_str = topic.__name__ if hasattr(topic, '__name__') else str(topic)
            function.__tag_for_sub__ = topic_str
            return function
        return decorator

    def subscribe_to_topic(self, event_type: Union[str, type], callback: Callable, priority: int = 0):
        """
        Subscribe a callback function to a specific event topic.

        Args:
            event_type: The event topic to subscribe to (string or event class)
            callback: The function to be called when the event is published
            priority: Priority of the subscription. Higher values indicate higher priority
        """
        # Convert event class to string if needed
        topic_str = event_type.__name__ if hasattr(event_type, '__name__') else str(event_type)

        logger.debug(f"Subscribing {callback} to {topic_str}")
        self.listeners[topic_str].append((callback, priority))
        self.listeners[topic_str].sort(key=lambda item: item[1], reverse=True)

        subscription = {
            "event_type": topic_str,
            "action": "subscribe",
            "timestamp": asyncio.get_event_loop().time(),
            "callback": callback
        }
        self.registry.append(subscription)

    def subscribe_tagged_methods(self, obj):
        """
        Subscribe methods of an object tagged with an event topic.

        Args:
            obj: The object whose tagged methods are to be subscribed
        """
        for name in dir(obj):
            method = getattr(obj, name)
            if hasattr(method, "__tag_for_sub__"):
                self.subscribe_to_topic(method.__tag_for_sub__, method)

    def unsubscribe(self, event_type: Union[str, type], callback: Callable):
        """
        Unsubscribe a callback function from a specific event topic.

        Args:
            event_type: The event topic to unsubscribe from
            callback: The function to be unsubscribed
        """
        # Convert event class to string if needed
        topic_str = event_type.__name__ if hasattr(event_type, '__name__') else str(event_type)

        if topic_str in self.listeners:
            self.listeners[topic_str] = [
                (cb, prio) for cb, prio in self.listeners[topic_str] if cb != callback
            ]

            unsubscription = {
                "event_type": topic_str,
                "action": "unsubscribe",
                "timestamp": asyncio.get_event_loop().time(),
                "callback": callback
            }
            self.registry.append(unsubscription)

    def unsubscribe_tagged_methods(self, obj):
        """
        Unsubscribe methods of an object tagged with an event topic.

        Args:
            obj: The object whose tagged methods are to be unsubscribed
        """
        for name in dir(obj):
            method = getattr(obj, name)
            if hasattr(method, "__tag_for_sub__"):
                self.unsubscribe(method.__tag_for_sub__, method)

    async def publish(self, event_type: Union[str, type, BaseEvent], message: Any = None):
        """
        Publish an event to all subscribed listeners.

        Args:
            event_type: The event topic to publish (string, event class, or event instance)
            message: Data to be sent with the event (optional if event_type is an instance)
        """
        try:
            # Handle different event_type formats
            if isinstance(event_type, BaseEvent):
                # If it's an event instance, use its class name and the instance as message
                topic_str = event_type.__class__.__name__
                event_data = event_type
            elif hasattr(event_type, '__name__'):
                # If it's an event class, use its name
                topic_str = event_type.__name__
                event_data = message
            else:
                # If it's a string, use it directly
                topic_str = str(event_type)
                event_data = message

            if topic_str in self.listeners:
                event = {
                    "event_type": topic_str,
                    "action": "publish",
                    "timestamp": asyncio.get_event_loop().time(),
                    "data": event_data
                }
                self.registry.append(event)

                tasks = [
                    asyncio.create_task(self._fire_event(callback, event_data))
                    for callback, _ in self.listeners[topic_str]
                ]
                await asyncio.sleep(0)
                await asyncio.gather(*tasks, return_exceptions=True)
        except Exception as e:
            logger.error(f"Error publishing event: {e}")
            traceback.print_exc()

    async def send(self, event_type: Union[str, type], message: Any = None):
        """
        Send an event to a single subscriber and return a Future for the result.

        Args:
            event_type: The event topic to send
            message: Data to be sent with the event

        Returns:
            asyncio.Future: Future for the result of the subscribed function
        """
        try:
            # Convert event class to string if needed
            topic_str = event_type.__name__ if hasattr(event_type, '__name__') else str(event_type)

            if topic_str in self.listeners:
                callback, _ = self.listeners[topic_str][0]  # Get the first subscriber

                # Create a future for the result
                result_future = asyncio.Future()

                # Define a callback function to set the result when the event is fired
                async def on_event_fired(result):
                    result_future.set_result(result)

                # Fire the event in the background
                asyncio.create_task(self._fire_event(callback, message)).add_done_callback(
                    lambda task: asyncio.create_task(on_event_fired(task.result()))
                )

                return result_future
        except Exception as e:
            logger.error(f"Error sending event: {e}")
            traceback.print_exc()

    async def _fire_event(self, callback: Callable, message: Any):
        """
        Fire an event by calling the callback with the message.

        Args:
            callback: The callback function to execute
            message: The message/event data to pass to the callback
        """
        try:
            if asyncio.iscoroutinefunction(callback):
                return await callback(message)
            else:
                return callback(message)
        except Exception as e:
            logger.error(f"Exception in event handler: {e}")
            logger.error(traceback.format_exc())
            # Don't exit in production, just log the error
            # sys.exit()

    def add_middleware(self, middleware: Callable) -> None:
        """
        Add middleware to process events before they reach handlers.

        Args:
            middleware: Function to process events
        """
        self._middleware.append(middleware)

    def get_registry(self):
        """Get the event registry for debugging and monitoring."""
        return self.registry

    def clear_subscribers(self) -> None:
        """Clear all event subscribers."""
        self.listeners.clear()
        logger.debug("Cleared all event subscribers")

    def get_subscriber_count(self, event_type: Optional[Union[str, type]] = None) -> Union[int, Dict[str, int]]:
        """
        Get the number of subscribers for an event type or all event types.

        Args:
            event_type: Specific event type to check (optional)

        Returns:
            Number of subscribers for the event type, or dict of all counts
        """
        if event_type:
            topic_str = event_type.__name__ if hasattr(event_type, '__name__') else str(event_type)
            return len(self.listeners.get(topic_str, []))
        else:
            return {topic: len(handlers) for topic, handlers in self.listeners.items()}


# Global event bus instance
event_bus = EventBus()
