"""
Validation utilities for the AI agent system.

This module provides common validation functions and utilities
that can be used across different components of the AI agent.
It includes data validation, type checking, format validation,
and business rule validation functions.

Validation categories:
- Data type and format validation
- Business rule validation
- Input sanitization and normalization
- Configuration validation
- API parameter validation
- User input validation

These utilities ensure data integrity and consistency across
the entire AI agent architecture.
"""

import re
import json
from typing import Any, Dict, List, Optional, Union, Tuple
from datetime import datetime, date
from email.utils import parseaddr
import uuid


class ValidationError(Exception):
    """Custom exception for validation errors."""
    
    def __init__(self, message: str, field: str = None, value: Any = None):
        self.message = message
        self.field = field
        self.value = value
        super().__init__(self.message)


class ValidationResult:
    """Result of a validation operation."""
    
    def __init__(self, is_valid: bool = True, errors: List[str] = None, warnings: List[str] = None):
        self.is_valid = is_valid
        self.errors = errors or []
        self.warnings = warnings or []
    
    def add_error(self, error: str) -> None:
        """Add an error to the validation result."""
        self.errors.append(error)
        self.is_valid = False
    
    def add_warning(self, warning: str) -> None:
        """Add a warning to the validation result."""
        self.warnings.append(warning)
    
    def merge(self, other: 'ValidationResult') -> None:
        """Merge another validation result into this one."""
        self.errors.extend(other.errors)
        self.warnings.extend(other.warnings)
        if not other.is_valid:
            self.is_valid = False


def validate_required_fields(data: Dict[str, Any], required_fields: List[str]) -> ValidationResult:
    """
    Validate that all required fields are present in the data.
    
    Args:
        data: Dictionary to validate
        required_fields: List of required field names
        
    Returns:
        ValidationResult with any missing field errors
    """
    result = ValidationResult()
    
    for field in required_fields:
        if field not in data or data[field] is None:
            result.add_error(f"Required field '{field}' is missing")
        elif isinstance(data[field], str) and not data[field].strip():
            result.add_error(f"Required field '{field}' cannot be empty")
    
    return result


def validate_string(value: Any, 
                   field_name: str = "field",
                   min_length: int = None,
                   max_length: int = None,
                   pattern: str = None,
                   allowed_values: List[str] = None) -> ValidationResult:
    """
    Validate a string value with various constraints.
    
    Args:
        value: Value to validate
        field_name: Name of the field being validated
        min_length: Minimum allowed length
        max_length: Maximum allowed length
        pattern: Regex pattern the string must match
        allowed_values: List of allowed values
        
    Returns:
        ValidationResult with any validation errors
    """
    result = ValidationResult()
    
    # Type check
    if not isinstance(value, str):
        result.add_error(f"{field_name} must be a string")
        return result
    
    # Length validation
    if min_length is not None and len(value) < min_length:
        result.add_error(f"{field_name} must be at least {min_length} characters long")
    
    if max_length is not None and len(value) > max_length:
        result.add_error(f"{field_name} must be no more than {max_length} characters long")
    
    # Pattern validation
    if pattern is not None:
        if not re.match(pattern, value):
            result.add_error(f"{field_name} does not match required pattern")
    
    # Allowed values validation
    if allowed_values is not None:
        if value not in allowed_values:
            result.add_error(f"{field_name} must be one of: {', '.join(allowed_values)}")
    
    return result


def validate_integer(value: Any,
                    field_name: str = "field",
                    min_value: int = None,
                    max_value: int = None) -> ValidationResult:
    """
    Validate an integer value with range constraints.
    
    Args:
        value: Value to validate
        field_name: Name of the field being validated
        min_value: Minimum allowed value
        max_value: Maximum allowed value
        
    Returns:
        ValidationResult with any validation errors
    """
    result = ValidationResult()
    
    # Type check
    if not isinstance(value, int):
        try:
            value = int(value)
        except (ValueError, TypeError):
            result.add_error(f"{field_name} must be an integer")
            return result
    
    # Range validation
    if min_value is not None and value < min_value:
        result.add_error(f"{field_name} must be at least {min_value}")
    
    if max_value is not None and value > max_value:
        result.add_error(f"{field_name} must be no more than {max_value}")
    
    return result


def validate_email(email: str, field_name: str = "email") -> ValidationResult:
    """
    Validate an email address format.
    
    Args:
        email: Email address to validate
        field_name: Name of the field being validated
        
    Returns:
        ValidationResult with any validation errors
    """
    result = ValidationResult()
    
    if not isinstance(email, str):
        result.add_error(f"{field_name} must be a string")
        return result
    
    # Basic email format validation
    parsed = parseaddr(email)
    if not parsed[1] or '@' not in parsed[1]:
        result.add_error(f"{field_name} is not a valid email address")
        return result
    
    # More detailed validation
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, email):
        result.add_error(f"{field_name} is not a valid email address")
    
    return result


def validate_url(url: str, field_name: str = "url", schemes: List[str] = None) -> ValidationResult:
    """
    Validate a URL format.
    
    Args:
        url: URL to validate
        field_name: Name of the field being validated
        schemes: Allowed URL schemes (default: ['http', 'https'])
        
    Returns:
        ValidationResult with any validation errors
    """
    result = ValidationResult()
    
    if not isinstance(url, str):
        result.add_error(f"{field_name} must be a string")
        return result
    
    if schemes is None:
        schemes = ['http', 'https']
    
    # Basic URL pattern validation
    url_pattern = r'^https?://(?:[-\w.])+(?:\:[0-9]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?$'
    
    if not re.match(url_pattern, url, re.IGNORECASE):
        result.add_error(f"{field_name} is not a valid URL")
        return result
    
    # Scheme validation
    scheme = url.split('://')[0].lower()
    if scheme not in schemes:
        result.add_error(f"{field_name} must use one of these schemes: {', '.join(schemes)}")
    
    return result


def validate_datetime(value: Any, 
                     field_name: str = "datetime",
                     format_string: str = None) -> ValidationResult:
    """
    Validate a datetime value.
    
    Args:
        value: Datetime value to validate (string or datetime object)
        field_name: Name of the field being validated
        format_string: Expected datetime format (if value is string)
        
    Returns:
        ValidationResult with any validation errors
    """
    result = ValidationResult()
    
    if isinstance(value, datetime):
        return result  # Already a valid datetime
    
    if isinstance(value, str):
        # Try to parse string as datetime
        if format_string:
            try:
                datetime.strptime(value, format_string)
                return result
            except ValueError:
                result.add_error(f"{field_name} does not match expected format: {format_string}")
                return result
        else:
            # Try common ISO formats
            iso_formats = [
                '%Y-%m-%dT%H:%M:%S',
                '%Y-%m-%dT%H:%M:%SZ',
                '%Y-%m-%dT%H:%M:%S.%f',
                '%Y-%m-%dT%H:%M:%S.%fZ',
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d'
            ]
            
            for fmt in iso_formats:
                try:
                    datetime.strptime(value, fmt)
                    return result
                except ValueError:
                    continue
            
            result.add_error(f"{field_name} is not a valid datetime format")
    else:
        result.add_error(f"{field_name} must be a datetime object or string")
    
    return result


def validate_uuid(value: Any, field_name: str = "uuid") -> ValidationResult:
    """
    Validate a UUID value.
    
    Args:
        value: UUID value to validate
        field_name: Name of the field being validated
        
    Returns:
        ValidationResult with any validation errors
    """
    result = ValidationResult()
    
    if isinstance(value, uuid.UUID):
        return result  # Already a valid UUID
    
    if isinstance(value, str):
        try:
            uuid.UUID(value)
            return result
        except ValueError:
            result.add_error(f"{field_name} is not a valid UUID")
    else:
        result.add_error(f"{field_name} must be a UUID string or UUID object")
    
    return result


def validate_json(value: Any, field_name: str = "json") -> ValidationResult:
    """
    Validate that a value is valid JSON.
    
    Args:
        value: Value to validate as JSON
        field_name: Name of the field being validated
        
    Returns:
        ValidationResult with any validation errors
    """
    result = ValidationResult()
    
    if isinstance(value, (dict, list)):
        return result  # Already parsed JSON
    
    if isinstance(value, str):
        try:
            json.loads(value)
            return result
        except json.JSONDecodeError as e:
            result.add_error(f"{field_name} is not valid JSON: {str(e)}")
    else:
        result.add_error(f"{field_name} must be a JSON string or object")
    
    return result


def validate_phone_number(phone: str, field_name: str = "phone") -> ValidationResult:
    """
    Validate a phone number format.
    
    Args:
        phone: Phone number to validate
        field_name: Name of the field being validated
        
    Returns:
        ValidationResult with any validation errors
    """
    result = ValidationResult()
    
    if not isinstance(phone, str):
        result.add_error(f"{field_name} must be a string")
        return result
    
    # Remove common formatting characters
    cleaned_phone = re.sub(r'[\s\-\(\)\+]', '', phone)
    
    # Basic phone number validation (digits only, reasonable length)
    if not cleaned_phone.isdigit():
        result.add_error(f"{field_name} must contain only digits and formatting characters")
        return result
    
    if len(cleaned_phone) < 7 or len(cleaned_phone) > 15:
        result.add_error(f"{field_name} must be between 7 and 15 digits long")
    
    return result


def sanitize_string(value: str, 
                   max_length: int = None,
                   remove_html: bool = True,
                   remove_special_chars: bool = False) -> str:
    """
    Sanitize a string value by removing or escaping potentially harmful content.
    
    Args:
        value: String to sanitize
        max_length: Maximum allowed length (truncate if longer)
        remove_html: Whether to remove HTML tags
        remove_special_chars: Whether to remove special characters
        
    Returns:
        Sanitized string
    """
    if not isinstance(value, str):
        return str(value)
    
    sanitized = value.strip()
    
    # Remove HTML tags if requested
    if remove_html:
        sanitized = re.sub(r'<[^>]+>', '', sanitized)
    
    # Remove special characters if requested
    if remove_special_chars:
        sanitized = re.sub(r'[^\w\s\-\.]', '', sanitized)
    
    # Remove null bytes and other control characters
    sanitized = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', sanitized)
    
    # Normalize whitespace
    sanitized = re.sub(r'\s+', ' ', sanitized).strip()
    
    # Truncate if necessary
    if max_length and len(sanitized) > max_length:
        sanitized = sanitized[:max_length].strip()
    
    return sanitized


def validate_configuration(config: Dict[str, Any], schema: Dict[str, Any]) -> ValidationResult:
    """
    Validate a configuration dictionary against a schema.
    
    Args:
        config: Configuration dictionary to validate
        schema: Schema defining expected structure and constraints
        
    Returns:
        ValidationResult with any validation errors
    """
    result = ValidationResult()
    
    # Check required fields
    required_fields = schema.get('required', [])
    required_validation = validate_required_fields(config, required_fields)
    result.merge(required_validation)
    
    # Validate individual fields
    properties = schema.get('properties', {})
    for field_name, field_schema in properties.items():
        if field_name in config:
            field_value = config[field_name]
            field_result = _validate_field_against_schema(field_value, field_schema, field_name)
            result.merge(field_result)
    
    # Check for unexpected fields
    allowed_fields = set(properties.keys())
    actual_fields = set(config.keys())
    unexpected_fields = actual_fields - allowed_fields
    
    for field in unexpected_fields:
        result.add_warning(f"Unexpected field '{field}' in configuration")
    
    return result


def _validate_field_against_schema(value: Any, schema: Dict[str, Any], field_name: str) -> ValidationResult:
    """Validate a single field against its schema definition."""
    result = ValidationResult()
    
    field_type = schema.get('type')
    
    if field_type == 'string':
        string_result = validate_string(
            value, field_name,
            min_length=schema.get('minLength'),
            max_length=schema.get('maxLength'),
            pattern=schema.get('pattern'),
            allowed_values=schema.get('enum')
        )
        result.merge(string_result)
    
    elif field_type == 'integer':
        integer_result = validate_integer(
            value, field_name,
            min_value=schema.get('minimum'),
            max_value=schema.get('maximum')
        )
        result.merge(integer_result)
    
    elif field_type == 'boolean':
        if not isinstance(value, bool):
            result.add_error(f"{field_name} must be a boolean")
    
    elif field_type == 'array':
        if not isinstance(value, list):
            result.add_error(f"{field_name} must be an array")
        else:
            # Validate array items if schema is provided
            items_schema = schema.get('items')
            if items_schema:
                for i, item in enumerate(value):
                    item_result = _validate_field_against_schema(item, items_schema, f"{field_name}[{i}]")
                    result.merge(item_result)
    
    elif field_type == 'object':
        if not isinstance(value, dict):
            result.add_error(f"{field_name} must be an object")
        else:
            # Recursively validate object properties
            object_result = validate_configuration(value, schema)
            result.merge(object_result)
    
    return result


def is_safe_filename(filename: str) -> bool:
    """
    Check if a filename is safe (no path traversal, etc.).
    
    Args:
        filename: Filename to check
        
    Returns:
        True if filename is safe, False otherwise
    """
    if not filename or not isinstance(filename, str):
        return False
    
    # Check for path traversal attempts
    if '..' in filename or '/' in filename or '\\' in filename:
        return False
    
    # Check for reserved names (Windows)
    reserved_names = ['CON', 'PRN', 'AUX', 'NUL'] + [f'COM{i}' for i in range(1, 10)] + [f'LPT{i}' for i in range(1, 10)]
    if filename.upper() in reserved_names:
        return False
    
    # Check for invalid characters
    invalid_chars = '<>:"|?*'
    if any(char in filename for char in invalid_chars):
        return False
    
    # Check length
    if len(filename) > 255:
        return False
    
    return True


def normalize_phone_number(phone: str) -> str:
    """
    Normalize a phone number to a standard format.
    
    Args:
        phone: Phone number to normalize
        
    Returns:
        Normalized phone number (digits only)
    """
    if not isinstance(phone, str):
        return ""
    
    # Remove all non-digit characters
    normalized = re.sub(r'\D', '', phone)
    
    # Remove leading country codes if present
    if normalized.startswith('1') and len(normalized) == 11:
        normalized = normalized[1:]  # Remove US country code
    
    return normalized
