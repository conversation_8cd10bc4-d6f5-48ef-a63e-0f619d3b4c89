"""
Constants and enumerations for the AI agent system.

This module defines all constants, enumerations, and configuration
values used throughout the AI agent architecture. It provides a
centralized location for system-wide constants to ensure consistency
and ease of maintenance.

Categories:
- System configuration constants
- Message and response limits
- Event types and status codes
- Error codes and messages
- Default values and timeouts
- API endpoints and versions
- File paths and naming conventions

These constants help maintain consistency across all components
and make the system easier to configure and maintain.
"""

from enum import Enum, IntEnum
from typing import Dict, Any


# System Information
SYSTEM_NAME = "Zeitwahl AI Assistant"
SYSTEM_VERSION = "1.0.0"
API_VERSION = "v1"

# Message Limits
MAX_MESSAGE_LENGTH = 4000
MIN_MESSAGE_LENGTH = 1
MAX_RESPONSE_LENGTH = 2000
MAX_PROMPT_LENGTH = 12000
MAX_CONTEXT_SIZE_CHARS = 8000

# Timeout Values (in seconds)
DEFAULT_TIMEOUT = 30
LLM_TIMEOUT = 60
TOOL_EXECUTION_TIMEOUT = 30
VALIDATION_TIMEOUT = 10
CONTEXT_BUILD_TIMEOUT = 30

# Rate Limiting
DEFAULT_RATE_LIMIT_CALLS = 30
DEFAULT_RATE_LIMIT_WINDOW_MINUTES = 1
MAX_CONCURRENT_EXECUTIONS = 5

# File and Directory Paths
LOG_DIRECTORY = "logs"
CONFIG_DIRECTORY = "config"
DATA_DIRECTORY = "data"
TEMP_DIRECTORY = "temp"

# Log File Names
MAIN_LOG_FILE = "zeitwahl.log"
ERROR_LOG_FILE = "zeitwahl_errors.log"
PERFORMANCE_LOG_FILE = "zeitwahl_performance.log"

# Database Configuration
DEFAULT_SCHEMA_USER = "user_data"
DEFAULT_SCHEMA_CALENDAR = "calendar_data"

# Table Names
TABLE_USERS = "users"
TABLE_CONVERSATIONS = "conversations"
TABLE_MESSAGES = "messages"
TABLE_EVENTS = "events"
TABLE_CALENDAR_LINKS = "calendar_links"

# Event Bus Configuration
MAX_EVENT_HISTORY = 1000
EVENT_PROCESSING_TIMEOUT = 30

# Quality Thresholds
MIN_RESPONSE_QUALITY_SCORE = 0.7
EXCELLENT_QUALITY_THRESHOLD = 0.9
GOOD_QUALITY_THRESHOLD = 0.8
ACCEPTABLE_QUALITY_THRESHOLD = 0.7
POOR_QUALITY_THRESHOLD = 0.5

# Validation Configuration
MAX_VALIDATION_ERRORS = 10
MAX_VALIDATION_WARNINGS = 20


class MessageType(Enum):
    """Types of messages in the system."""
    TEXT = "text"
    COMMAND = "command"
    IMAGE = "image"
    FILE = "file"
    VOICE = "voice"
    LOCATION = "location"


class ResponseType(Enum):
    """Types of responses the system can generate."""
    TEXT = "text"
    IMAGE = "image"
    FILE = "file"
    CALENDAR_EVENT = "calendar_event"
    REMINDER = "reminder"
    ERROR = "error"


class EventType(Enum):
    """Types of events in the event bus system."""
    MESSAGE_RECEIVED = "message_received"
    MESSAGE_PREPROCESSED = "message_preprocessed"
    LLM_RESPONSE_RECEIVED = "llm_response_received"
    TOOL_EXECUTION_STARTED = "tool_execution_started"
    TOOL_EXECUTION_COMPLETED = "tool_execution_completed"
    RESPONSE_READY = "response_ready"
    ERROR_OCCURRED = "error_occurred"
    USER_SESSION_STARTED = "user_session_started"
    USER_SESSION_ENDED = "user_session_ended"
    SYSTEM_HEALTH_CHECK = "system_health_check"


class ProcessingStage(Enum):
    """Stages in the message processing pipeline."""
    RECEIVED = "received"
    VALIDATING = "validating"
    IDENTIFYING_USER = "identifying_user"
    BUILDING_CONTEXT = "building_context"
    BUILDING_PROMPT = "building_prompt"
    CALLING_LLM = "calling_llm"
    VALIDATING_RESPONSE = "validating_response"
    EXECUTING_TOOLS = "executing_tools"
    EVALUATING_RESPONSE = "evaluating_response"
    PREPARING_RESPONSE = "preparing_response"
    COMPLETED = "completed"
    FAILED = "failed"


class ErrorType(Enum):
    """Types of errors that can occur in the system."""
    VALIDATION_ERROR = "validation_error"
    USER_IDENTIFICATION_ERROR = "user_identification_error"
    CONTEXT_BUILDING_ERROR = "context_building_error"
    PROMPT_BUILDING_ERROR = "prompt_building_error"
    LLM_ERROR = "llm_error"
    TOOL_EXECUTION_ERROR = "tool_execution_error"
    RESPONSE_VALIDATION_ERROR = "response_validation_error"
    RESPONSE_EVALUATION_ERROR = "response_evaluation_error"
    NETWORK_ERROR = "network_error"
    DATABASE_ERROR = "database_error"
    CONFIGURATION_ERROR = "configuration_error"
    UNEXPECTED_ERROR = "unexpected_error"


class ToolStatus(Enum):
    """Status of tool execution."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"


class QualityLevel(Enum):
    """Quality levels for response evaluation."""
    EXCELLENT = "excellent"
    GOOD = "good"
    ACCEPTABLE = "acceptable"
    POOR = "poor"
    UNACCEPTABLE = "unacceptable"


class ComponentStatus(Enum):
    """Health status of system components."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


class LogLevel(IntEnum):
    """Log levels for the system."""
    DEBUG = 10
    INFO = 20
    WARNING = 30
    ERROR = 40
    CRITICAL = 50


class Platform(Enum):
    """Supported platforms for the bot."""
    TELEGRAM = "telegram"
    DISCORD = "discord"
    SLACK = "slack"
    WHATSAPP = "whatsapp"
    WEB = "web"


class Language(Enum):
    """Supported languages."""
    ENGLISH = "en"
    GERMAN = "de"
    SPANISH = "es"
    FRENCH = "fr"
    ITALIAN = "it"
    PORTUGUESE = "pt"
    RUSSIAN = "ru"
    CHINESE = "zh"
    JAPANESE = "ja"
    KOREAN = "ko"


class TimeZone(Enum):
    """Common time zones."""
    UTC = "UTC"
    US_EASTERN = "US/Eastern"
    US_CENTRAL = "US/Central"
    US_MOUNTAIN = "US/Mountain"
    US_PACIFIC = "US/Pacific"
    EUROPE_LONDON = "Europe/London"
    EUROPE_PARIS = "Europe/Paris"
    EUROPE_BERLIN = "Europe/Berlin"
    ASIA_TOKYO = "Asia/Tokyo"
    ASIA_SHANGHAI = "Asia/Shanghai"
    AUSTRALIA_SYDNEY = "Australia/Sydney"


# Error Messages
ERROR_MESSAGES = {
    ErrorType.VALIDATION_ERROR: "The provided input is not valid. Please check your request and try again.",
    ErrorType.USER_IDENTIFICATION_ERROR: "Unable to identify user. Please try again.",
    ErrorType.CONTEXT_BUILDING_ERROR: "Unable to gather necessary context. Please try again.",
    ErrorType.PROMPT_BUILDING_ERROR: "Unable to process your request. Please try again.",
    ErrorType.LLM_ERROR: "AI service is temporarily unavailable. Please try again later.",
    ErrorType.TOOL_EXECUTION_ERROR: "Unable to complete the requested action. Please try again.",
    ErrorType.RESPONSE_VALIDATION_ERROR: "Response validation failed. Please try again.",
    ErrorType.NETWORK_ERROR: "Network connection issue. Please check your connection and try again.",
    ErrorType.DATABASE_ERROR: "Database service is temporarily unavailable. Please try again later.",
    ErrorType.CONFIGURATION_ERROR: "System configuration error. Please contact support.",
    ErrorType.UNEXPECTED_ERROR: "An unexpected error occurred. Please try again."
}

# Default Configuration Values
DEFAULT_CONFIG = {
    "message_validator": {
        "max_message_length": MAX_MESSAGE_LENGTH,
        "min_message_length": MIN_MESSAGE_LENGTH,
        "allow_empty_messages": False,
        "rate_limit_window_minutes": DEFAULT_RATE_LIMIT_WINDOW_MINUTES,
        "rate_limit_max_messages": DEFAULT_RATE_LIMIT_CALLS,
        "blocked_patterns": [],
        "required_sanitization": True
    },
    "context_builder": {
        "max_conversation_history": 10,
        "include_user_preferences": True,
        "include_temporal_context": True,
        "include_external_apis": True,
        "context_timeout_seconds": CONTEXT_BUILD_TIMEOUT,
        "max_context_size_chars": MAX_CONTEXT_SIZE_CHARS
    },
    "prompt_builder": {
        "max_prompt_length": MAX_PROMPT_LENGTH,
        "include_system_instructions": True,
        "include_tool_definitions": True,
        "include_examples": True,
        "prompt_format": "structured",
        "truncation_strategy": "smart"
    },
    "response_validator": {
        "max_response_length": MAX_RESPONSE_LENGTH,
        "min_response_length": 5,
        "require_helpful_response": True,
        "check_content_safety": True,
        "validate_tool_usage": True,
        "check_response_relevance": True,
        "quality_threshold": MIN_RESPONSE_QUALITY_SCORE
    },
    "tool_executor": {
        "max_concurrent_executions": MAX_CONCURRENT_EXECUTIONS,
        "default_timeout_seconds": TOOL_EXECUTION_TIMEOUT,
        "enable_rate_limiting": True,
        "max_execution_time_ms": 60000,
        "allow_parallel_execution": True
    },
    "response_evaluator": {
        "weights": {
            "completeness": 0.25,
            "accuracy": 0.25,
            "helpfulness": 0.20,
            "personalization": 0.15,
            "tool_usage": 0.10,
            "coherence": 0.05
        },
        "minimum_acceptable_score": MIN_RESPONSE_QUALITY_SCORE,
        "quality_thresholds": {
            "excellent": EXCELLENT_QUALITY_THRESHOLD,
            "good": GOOD_QUALITY_THRESHOLD,
            "acceptable": ACCEPTABLE_QUALITY_THRESHOLD,
            "poor": POOR_QUALITY_THRESHOLD
        }
    }
}

# API Endpoints (for external integrations)
API_ENDPOINTS = {
    "calendar": {
        "google": "https://www.googleapis.com/calendar/v3",
        "outlook": "https://graph.microsoft.com/v1.0/me/calendar"
    },
    "weather": {
        "openweather": "https://api.openweathermap.org/data/2.5",
        "weatherapi": "https://api.weatherapi.com/v1"
    },
    "llm": {
        "openai": "https://api.openai.com/v1",
        "anthropic": "https://api.anthropic.com/v1",
        "local": "http://localhost:8000/v1"
    }
}

# File Extensions
ALLOWED_FILE_EXTENSIONS = {
    "images": [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"],
    "documents": [".pdf", ".doc", ".docx", ".txt", ".rtf"],
    "spreadsheets": [".xls", ".xlsx", ".csv"],
    "presentations": [".ppt", ".pptx"],
    "archives": [".zip", ".rar", ".7z", ".tar", ".gz"]
}

# MIME Types
MIME_TYPES = {
    "text": "text/plain",
    "html": "text/html",
    "json": "application/json",
    "pdf": "application/pdf",
    "image_jpeg": "image/jpeg",
    "image_png": "image/png"
}

# Regular Expressions
REGEX_PATTERNS = {
    "email": r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    "phone": r'^\+?[\d\s\-\(\)]{7,15}$',
    "url": r'^https?://(?:[-\w.])+(?:\:[0-9]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?$',
    "uuid": r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$',
    "datetime_iso": r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d{3})?(?:Z|[+-]\d{2}:\d{2})$',
    "telegram_token": r'^\d+:[A-Za-z0-9_-]{35}$'
}

# Feature Flags
FEATURE_FLAGS = {
    "enable_response_caching": False,
    "enable_conversation_memory": True,
    "enable_user_analytics": True,
    "enable_performance_monitoring": True,
    "enable_a_b_testing": False,
    "enable_debug_mode": False,
    "enable_rate_limiting": True,
    "enable_content_moderation": True
}

# Cache Configuration
CACHE_CONFIG = {
    "default_ttl_seconds": 3600,  # 1 hour
    "max_cache_size_mb": 100,
    "cache_key_prefix": "zeitwahl:",
    "enable_cache_compression": True
}
