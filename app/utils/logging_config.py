"""
Logging configuration utilities for the AI agent.

This module provides centralized logging configuration and utilities
for consistent logging across all components of the AI agent system.
It supports structured logging, different log levels, and various
output formats for development and production environments.

Features:
- Structured JSON logging for production
- Human-readable logging for development
- Component-specific loggers with appropriate levels
- Log filtering and formatting utilities
- Performance logging helpers
- Error tracking and correlation IDs

The logging system ensures comprehensive observability across
the entire AI agent architecture.
"""

import logging
import logging.config
import json
import sys
from typing import Dict, Any, Optional
from datetime import datetime
import uuid
import traceback


class StructuredFormatter(logging.Formatter):
    """
    Custom formatter for structured JSON logging.
    
    Formats log records as JSON with consistent structure for
    easy parsing and analysis in log aggregation systems.
    """
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as structured JSON."""
        log_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Add correlation ID if available
        if hasattr(record, 'correlation_id'):
            log_entry['correlation_id'] = record.correlation_id
        
        # Add user context if available
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
        
        if hasattr(record, 'chat_id'):
            log_entry['chat_id'] = record.chat_id
        
        # Add component information
        if hasattr(record, 'component'):
            log_entry['component'] = record.component
        
        # Add exception information if present
        if record.exc_info:
            log_entry['exception'] = {
                'type': record.exc_info[0].__name__ if record.exc_info[0] else None,
                'message': str(record.exc_info[1]) if record.exc_info[1] else None,
                'traceback': traceback.format_exception(*record.exc_info)
            }
        
        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 
                          'filename', 'module', 'lineno', 'funcName', 'created', 
                          'msecs', 'relativeCreated', 'thread', 'threadName', 
                          'processName', 'process', 'getMessage', 'exc_info', 
                          'exc_text', 'stack_info', 'correlation_id', 'user_id', 
                          'chat_id', 'component']:
                if not key.startswith('_'):
                    log_entry['extra'] = log_entry.get('extra', {})
                    log_entry['extra'][key] = value
        
        return json.dumps(log_entry, default=str)


class ContextFilter(logging.Filter):
    """
    Filter to add context information to log records.
    
    Automatically adds correlation IDs, user context, and component
    information to log records for better traceability.
    """
    
    def __init__(self):
        super().__init__()
        self._correlation_id = None
        self._user_context = {}
        self._component = None
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Add context information to log record."""
        # Add correlation ID
        if self._correlation_id:
            record.correlation_id = self._correlation_id
        
        # Add user context
        if self._user_context:
            if 'user_id' in self._user_context:
                record.user_id = self._user_context['user_id']
            if 'chat_id' in self._user_context:
                record.chat_id = self._user_context['chat_id']
        
        # Add component information
        if self._component:
            record.component = self._component
        
        return True
    
    def set_correlation_id(self, correlation_id: str) -> None:
        """Set correlation ID for current context."""
        self._correlation_id = correlation_id
    
    def set_user_context(self, user_id: Optional[int] = None, chat_id: Optional[int] = None) -> None:
        """Set user context for current context."""
        self._user_context = {}
        if user_id is not None:
            self._user_context['user_id'] = user_id
        if chat_id is not None:
            self._user_context['chat_id'] = chat_id
    
    def set_component(self, component: str) -> None:
        """Set component name for current context."""
        self._component = component
    
    def clear_context(self) -> None:
        """Clear all context information."""
        self._correlation_id = None
        self._user_context = {}
        self._component = None


class PerformanceLogger:
    """
    Utility for performance logging and timing.
    
    Provides context managers and decorators for measuring
    and logging performance metrics across the system.
    """
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
    
    def __enter__(self):
        self.start_time = datetime.now()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = (datetime.now() - self.start_time).total_seconds() * 1000
        self.logger.info(f"Operation completed", extra={'duration_ms': duration})
    
    def log_timing(self, operation: str, duration_ms: float, **kwargs) -> None:
        """Log timing information for an operation."""
        extra = {'operation': operation, 'duration_ms': duration_ms}
        extra.update(kwargs)
        self.logger.info(f"Performance: {operation} took {duration_ms:.2f}ms", extra=extra)
    
    def log_metrics(self, metrics: Dict[str, Any]) -> None:
        """Log performance metrics."""
        self.logger.info("Performance metrics", extra={'metrics': metrics})


# Global context filter instance
_context_filter = ContextFilter()


def setup_logging(config: Dict[str, Any] = None) -> None:
    """
    Set up logging configuration for the AI agent.
    
    Args:
        config: Logging configuration dictionary
    """
    # Default configuration
    default_config = {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'structured': {
                '()': StructuredFormatter,
            },
            'simple': {
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            },
            'detailed': {
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(module)s:%(funcName)s:%(lineno)d - %(message)s'
            }
        },
        'filters': {
            'context_filter': {
                '()': lambda: _context_filter
            }
        },
        'handlers': {
            'console': {
                'class': 'logging.StreamHandler',
                'level': 'INFO',
                'formatter': 'simple',
                'stream': sys.stdout,
                'filters': ['context_filter']
            },
            'file': {
                'class': 'logging.handlers.RotatingFileHandler',
                'level': 'DEBUG',
                'formatter': 'detailed',
                'filename': 'logs/zeitwahl.log',
                'maxBytes': 10485760,  # 10MB
                'backupCount': 5,
                'filters': ['context_filter']
            },
            'error_file': {
                'class': 'logging.handlers.RotatingFileHandler',
                'level': 'ERROR',
                'formatter': 'structured',
                'filename': 'logs/zeitwahl_errors.log',
                'maxBytes': 10485760,  # 10MB
                'backupCount': 5,
                'filters': ['context_filter']
            }
        },
        'loggers': {
            'app': {
                'level': 'DEBUG',
                'handlers': ['console', 'file', 'error_file'],
                'propagate': False
            },
            'app.preprocess': {
                'level': 'DEBUG',
                'handlers': ['console', 'file'],
                'propagate': False
            },
            'app.postprocess': {
                'level': 'DEBUG',
                'handlers': ['console', 'file'],
                'propagate': False
            },
            'app.bot': {
                'level': 'INFO',
                'handlers': ['console', 'file'],
                'propagate': False
            },
            'app.utils': {
                'level': 'INFO',
                'handlers': ['console', 'file'],
                'propagate': False
            }
        },
        'root': {
            'level': 'WARNING',
            'handlers': ['console']
        }
    }
    
    # Merge with provided config
    if config:
        # Simple merge - in production, use more sophisticated merging
        default_config.update(config)
    
    # Create logs directory if it doesn't exist
    import os
    os.makedirs('logs', exist_ok=True)
    
    # Apply configuration
    logging.config.dictConfig(default_config)


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with the specified name.
    
    Args:
        name: Logger name (typically __name__)
        
    Returns:
        Configured logger instance
    """
    return logging.getLogger(name)


def set_correlation_id(correlation_id: Optional[str] = None) -> str:
    """
    Set correlation ID for current context.
    
    Args:
        correlation_id: Correlation ID to set, or None to generate new one
        
    Returns:
        The correlation ID that was set
    """
    if correlation_id is None:
        correlation_id = str(uuid.uuid4())
    
    _context_filter.set_correlation_id(correlation_id)
    return correlation_id


def set_user_context(user_id: Optional[int] = None, chat_id: Optional[int] = None) -> None:
    """
    Set user context for current logging context.
    
    Args:
        user_id: User ID to include in logs
        chat_id: Chat ID to include in logs
    """
    _context_filter.set_user_context(user_id, chat_id)


def set_component(component: str) -> None:
    """
    Set component name for current logging context.
    
    Args:
        component: Component name to include in logs
    """
    _context_filter.set_component(component)


def clear_logging_context() -> None:
    """Clear all logging context information."""
    _context_filter.clear_context()


def get_performance_logger(logger_name: str) -> PerformanceLogger:
    """
    Get a performance logger for timing operations.
    
    Args:
        logger_name: Name of the logger to use
        
    Returns:
        PerformanceLogger instance
    """
    logger = get_logger(logger_name)
    return PerformanceLogger(logger)


# Convenience function for timing operations
def log_performance(operation: str, logger_name: str = None):
    """
    Decorator for logging performance of functions.
    
    Args:
        operation: Name of the operation being timed
        logger_name: Logger name to use (defaults to function module)
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = datetime.now()
            logger = get_logger(logger_name or func.__module__)
            
            try:
                result = func(*args, **kwargs)
                duration = (datetime.now() - start_time).total_seconds() * 1000
                logger.info(f"Performance: {operation} completed", 
                           extra={'operation': operation, 'duration_ms': duration, 'success': True})
                return result
            except Exception as e:
                duration = (datetime.now() - start_time).total_seconds() * 1000
                logger.error(f"Performance: {operation} failed", 
                           extra={'operation': operation, 'duration_ms': duration, 'success': False, 'error': str(e)})
                raise
        
        return wrapper
    return decorator


# Initialize logging with default configuration
setup_logging()
