"""
Event definitions for the AI agent system.

This module defines all the events that can be published and subscribed to
throughout the AI agent architecture. Events represent significant occurrences
in the system and enable decoupled communication between components.

Event Flow:
1. MessageReceived -> triggers preprocessing
2. MessagePreprocessed -> triggers LLM call
3. LLMResponseReceived -> triggers postprocessing
4. ResponseReady -> triggers bot response
5. ErrorOccurred -> triggers error handling

Each event contains the necessary data for handlers to process the event
without needing to query external systems.
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional, List
from datetime import datetime

from .event_bus import BaseEvent


@dataclass
class MessageReceived(BaseEvent):
    """
    Event published when a new message is received from the bot platform.
    
    This event triggers the preprocessing pipeline to prepare the message
    for LLM processing.
    """
    user_id: int
    chat_id: int
    message_text: str
    message_id: str
    timestamp: datetime
    platform: str = "telegram"
    metadata: Dict[str, Any] = None


@dataclass
class MessagePreprocessed(BaseEvent):
    """
    Event published when message preprocessing is complete.
    
    Contains the prepared prompt and context ready for LLM processing.
    """
    user_id: int
    chat_id: int
    original_message: str
    prepared_prompt: str
    context: Dict[str, Any]
    available_tools: List[str]
    conversation_history: List[Dict[str, Any]]
    preprocessing_metadata: Dict[str, Any] = None


@dataclass
class LLMResponseReceived(BaseEvent):
    """
    Event published when an LLM response is received.
    
    Triggers postprocessing to validate and execute the response.
    """
    user_id: int
    chat_id: int
    llm_response: str
    response_metadata: Dict[str, Any]
    tools_to_execute: List[Dict[str, Any]]
    confidence_score: Optional[float] = None
    processing_time_ms: Optional[int] = None


@dataclass
class ToolExecutionStarted(BaseEvent):
    """
    Event published when tool execution begins.
    
    Allows monitoring and logging of tool execution progress.
    """
    user_id: int
    chat_id: int
    tool_name: str
    tool_parameters: Dict[str, Any]
    execution_id: str


@dataclass
class ToolExecutionCompleted(BaseEvent):
    """
    Event published when tool execution completes.
    
    Contains the results of tool execution for further processing.
    """
    user_id: int
    chat_id: int
    tool_name: str
    execution_id: str
    success: bool
    result: Any
    error_message: Optional[str] = None
    execution_time_ms: Optional[int] = None


@dataclass
class ResponseReady(BaseEvent):
    """
    Event published when the final response is ready to send to the user.
    
    Triggers the bot to send the response back to the user.
    """
    user_id: int
    chat_id: int
    response_text: str
    response_type: str = "text"  # text, image, file, etc.
    attachments: List[Dict[str, Any]] = None
    metadata: Dict[str, Any] = None


@dataclass
class ErrorOccurred(BaseEvent):
    """
    Event published when an error occurs in any part of the system.
    
    Enables centralized error handling and logging.
    """
    user_id: Optional[int]
    chat_id: Optional[int]
    error_type: str
    error_message: str
    component: str
    stack_trace: Optional[str] = None
    context: Dict[str, Any] = None
    timestamp: datetime = None


@dataclass
class UserSessionStarted(BaseEvent):
    """
    Event published when a user starts a new session.
    
    Triggers user context loading and session initialization.
    """
    user_id: int
    chat_id: int
    platform: str
    user_metadata: Dict[str, Any]


@dataclass
class UserSessionEnded(BaseEvent):
    """
    Event published when a user session ends.
    
    Triggers session cleanup and context saving.
    """
    user_id: int
    chat_id: int
    session_duration_ms: int
    messages_processed: int
    metadata: Dict[str, Any] = None


@dataclass
class SystemHealthCheck(BaseEvent):
    """
    Event published for system health monitoring.
    
    Allows components to report their health status.
    """
    component: str
    status: str  # healthy, degraded, unhealthy
    metrics: Dict[str, Any]
    timestamp: datetime
