"""
LLM (Large Language Model) service for AI agent interactions.

This service provides a unified interface for interacting with various
LLM providers including OpenAI, Anthropic, and local models. It handles
prompt formatting, response parsing, error handling, and provider-specific
configurations.

Features:
- Multiple LLM provider support
- Automatic failover between providers
- Response streaming and batching
- Token usage tracking and optimization
- Rate limiting and quota management
- Response caching for efficiency
- Model-specific prompt formatting

The service abstracts away provider-specific details and provides
a consistent interface for the rest of the AI agent system.
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, AsyncGenerator
from datetime import datetime
import json

from ..utils.events import LLMResponseReceived, ErrorOccurred
from ..utils.event_bus import event_bus
from ..utils.constants import ErrorType, DEFAULT_TIMEOUT

logger = logging.getLogger(__name__)


class LLMProvider:
    """Base class for LLM providers."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.name = "base"
    
    async def generate_response(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate a response from the LLM."""
        raise NotImplementedError("Subclasses must implement generate_response")
    
    async def stream_response(self, prompt: str, **kwargs) -> AsyncGenerator[str, None]:
        """Stream a response from the LLM."""
        raise NotImplementedError("Subclasses must implement stream_response")
        yield  # This line will never be reached but satisfies the type checker
    
    def is_available(self) -> bool:
        """Check if the provider is available."""
        return True
    
    def get_token_count(self, text: str) -> int:
        """Estimate token count for the given text."""
        # Simple estimation - in production, use proper tokenizers
        return int(len(text.split()) * 1.3)  # Rough approximation


class OpenAIProvider(LLMProvider):
    """OpenAI LLM provider implementation."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.name = "openai"
        self.api_key = config.get('api_key')
        self.model = config.get('model', 'gpt-3.5-turbo')
        self.base_url = config.get('base_url', 'https://api.openai.com/v1')
    
    async def generate_response(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate response using OpenAI API."""
        try:
            # Simulate API call - in production, use actual OpenAI client
            await asyncio.sleep(0.1)  # Simulate network delay
            
            response_text = f"OpenAI response to: {prompt[:50]}..."
            
            return {
                'response': response_text,
                'model': self.model,
                'provider': self.name,
                'tokens_used': self.get_token_count(prompt + response_text),
                'finish_reason': 'stop',
                'metadata': {
                    'model_version': self.model,
                    'api_version': 'v1',
                    'response_time_ms': 100
                }
            }
        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            raise
    
    async def stream_response(self, prompt: str, **kwargs) -> AsyncGenerator[str, None]:
        """Stream response using OpenAI API."""
        # Simulate streaming response
        response_parts = ["This ", "is ", "a ", "streamed ", "response ", "from ", "OpenAI."]
        
        for part in response_parts:
            await asyncio.sleep(0.1)  # Simulate streaming delay
            yield part
    
    def is_available(self) -> bool:
        """Check if OpenAI API is available."""
        return bool(self.api_key)


class AnthropicProvider(LLMProvider):
    """Anthropic (Claude) LLM provider implementation."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.name = "anthropic"
        self.api_key = config.get('api_key')
        self.model = config.get('model', 'claude-3-sonnet-20240229')
        self.base_url = config.get('base_url', 'https://api.anthropic.com/v1')
    
    async def generate_response(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate response using Anthropic API."""
        try:
            # Simulate API call - in production, use actual Anthropic client
            await asyncio.sleep(0.1)  # Simulate network delay
            
            response_text = f"Anthropic Claude response to: {prompt[:50]}..."
            
            return {
                'response': response_text,
                'model': self.model,
                'provider': self.name,
                'tokens_used': self.get_token_count(prompt + response_text),
                'finish_reason': 'stop',
                'metadata': {
                    'model_version': self.model,
                    'api_version': 'v1',
                    'response_time_ms': 120
                }
            }
        except Exception as e:
            logger.error(f"Anthropic API error: {e}")
            raise
    
    async def stream_response(self, prompt: str, **kwargs) -> AsyncGenerator[str, None]:
        """Stream response using Anthropic API."""
        # Simulate streaming response
        response_parts = ["This ", "is ", "a ", "streamed ", "response ", "from ", "Claude."]
        
        for part in response_parts:
            await asyncio.sleep(0.1)  # Simulate streaming delay
            yield part
    
    def is_available(self) -> bool:
        """Check if Anthropic API is available."""
        return bool(self.api_key)


class LocalProvider(LLMProvider):
    """Local LLM provider implementation."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.name = "local"
        self.model = config.get('model', 'local-llm')
        self.base_url = config.get('base_url', 'http://localhost:8000')
    
    async def generate_response(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate response using local LLM."""
        try:
            # Simulate local model call
            await asyncio.sleep(0.2)  # Simulate processing time
            
            response_text = f"Local model response to: {prompt[:50]}..."
            
            return {
                'response': response_text,
                'model': self.model,
                'provider': self.name,
                'tokens_used': self.get_token_count(prompt + response_text),
                'finish_reason': 'stop',
                'metadata': {
                    'model_version': self.model,
                    'api_version': 'local',
                    'response_time_ms': 200
                }
            }
        except Exception as e:
            logger.error(f"Local LLM error: {e}")
            raise
    
    async def stream_response(self, prompt: str, **kwargs) -> AsyncGenerator[str, None]:
        """Stream response using local LLM."""
        # Simulate streaming response
        response_parts = ["This ", "is ", "a ", "streamed ", "response ", "from ", "local ", "model."]
        
        for part in response_parts:
            await asyncio.sleep(0.1)  # Simulate streaming delay
            yield part
    
    def is_available(self) -> bool:
        """Check if local LLM is available."""
        # In production, check if local model server is running
        return True


class LLMService:
    """
    Main LLM service that manages multiple providers and handles requests.
    
    Provides a unified interface for LLM interactions with automatic
    failover, load balancing, and provider management.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the LLM service.
        
        Args:
            config: Configuration for LLM providers and service settings
        """
        self.config = config or {}
        self.providers: Dict[str, LLMProvider] = {}
        self.primary_provider = None
        self.fallback_providers = []
        
        # Service configuration
        self.enable_failover = self.config.get('enable_failover', True)
        self.enable_caching = self.config.get('enable_caching', False)
        self.max_retries = self.config.get('max_retries', 2)
        self.timeout_seconds = self.config.get('timeout_seconds', DEFAULT_TIMEOUT)
        
        # Initialize providers
        self._initialize_providers()
        
        # Subscribe to message preprocessed events using the new event bus
        event_bus.subscribe_tagged_methods(self)
        
        logger.info(f"LLM service initialized with {len(self.providers)} providers")
    
    def _initialize_providers(self) -> None:
        """Initialize LLM providers based on configuration."""
        provider_configs = self.config.get('providers', {})
        
        # Initialize OpenAI provider
        if 'openai' in provider_configs:
            self.providers['openai'] = OpenAIProvider(provider_configs['openai'])
        
        # Initialize Anthropic provider
        if 'anthropic' in provider_configs:
            self.providers['anthropic'] = AnthropicProvider(provider_configs['anthropic'])
        
        # Initialize local provider
        if 'local' in provider_configs:
            self.providers['local'] = LocalProvider(provider_configs['local'])
        
        # Set primary and fallback providers
        provider_priority = self.config.get('provider_priority', ['openai', 'anthropic', 'local'])
        
        for provider_name in provider_priority:
            if provider_name in self.providers and self.providers[provider_name].is_available():
                if self.primary_provider is None:
                    self.primary_provider = provider_name
                else:
                    self.fallback_providers.append(provider_name)
        
        if not self.primary_provider:
            logger.warning("No available LLM providers found")
    
    @event_bus.subscribe("MessagePreprocessed")
    async def handle_preprocessed_message(self, event) -> None:
        """
        Handle preprocessed message events by calling the LLM.
        
        Args:
            event: MessagePreprocessed event containing the prepared prompt
        """
        try:
            logger.info(f"Processing LLM request for user {event.user_id}")
            
            # Generate response using LLM
            llm_response = await self.generate_response(
                prompt=event.prepared_prompt,
                user_id=event.user_id,
                chat_id=event.chat_id,
                context=event.context
            )
            
            # Extract tools from response if any
            tools_to_execute = self._extract_tools_from_response(llm_response['response'])
            
            # Publish LLM response event
            await event_bus.publish(LLMResponseReceived(
                user_id=event.user_id,
                chat_id=event.chat_id,
                llm_response=llm_response['response'],
                response_metadata={
                    'model': llm_response['model'],
                    'provider': llm_response['provider'],
                    'tokens_used': llm_response['tokens_used'],
                    'finish_reason': llm_response['finish_reason'],
                    'original_prompt': event.prepared_prompt,
                    'processing_time_ms': llm_response.get('metadata', {}).get('response_time_ms')
                },
                tools_to_execute=tools_to_execute,
                confidence_score=self._calculate_confidence_score(llm_response),
                processing_time_ms=llm_response.get('metadata', {}).get('response_time_ms')
            ))
            
            logger.info(f"LLM response generated for user {event.user_id}")
            
        except Exception as e:
            logger.error(f"Error processing LLM request for user {event.user_id}: {e}", exc_info=True)
            
            # Publish error event
            await event_bus.publish(ErrorOccurred(
                user_id=event.user_id,
                chat_id=event.chat_id,
                error_type=ErrorType.LLM_ERROR.value,
                error_message=str(e),
                component="llm_service",
                timestamp=datetime.now()
            ))
    
    async def generate_response(self,
                              prompt: str,
                              user_id: Optional[int] = None,
                              chat_id: Optional[int] = None,
                              context: Optional[Dict[str, Any]] = None,
                              **kwargs) -> Dict[str, Any]:
        """
        Generate a response using the configured LLM providers.
        
        Args:
            prompt: The prompt to send to the LLM
            user_id: User ID for context
            chat_id: Chat ID for context
            context: Additional context information
            **kwargs: Additional parameters for the LLM
            
        Returns:
            Dictionary containing the LLM response and metadata
        """
        providers_to_try = [self.primary_provider] + self.fallback_providers
        
        for provider_name in providers_to_try:
            if provider_name not in self.providers:
                continue
            
            provider = self.providers[provider_name]
            
            try:
                logger.debug(f"Attempting LLM request with provider: {provider_name}")
                
                # Add timeout to the request
                response = await asyncio.wait_for(
                    provider.generate_response(prompt, **kwargs),
                    timeout=self.timeout_seconds
                )
                
                logger.info(f"LLM response generated successfully using {provider_name}")
                return response
                
            except asyncio.TimeoutError:
                logger.warning(f"LLM request timed out with provider: {provider_name}")
                if not self.enable_failover:
                    raise
                continue
                
            except Exception as e:
                logger.warning(f"LLM request failed with provider {provider_name}: {e}")
                if not self.enable_failover:
                    raise
                continue
        
        # If all providers failed
        raise Exception("All LLM providers failed to generate a response")
    
    async def stream_response(self,
                            prompt: str,
                            provider_name: Optional[str] = None,
                            **kwargs) -> AsyncGenerator[str, None]:
        """
        Stream a response from the LLM.
        
        Args:
            prompt: The prompt to send to the LLM
            provider_name: Specific provider to use (optional)
            **kwargs: Additional parameters for the LLM
            
        Yields:
            Chunks of the response as they are generated
        """
        if provider_name and provider_name in self.providers:
            provider = self.providers[provider_name]
        elif self.primary_provider:
            provider = self.providers[self.primary_provider]
        else:
            raise Exception("No LLM providers available")
        
        stream = provider.stream_response(prompt, **kwargs)
        async for chunk in stream:
            yield chunk
    
    def _extract_tools_from_response(self, response: str) -> List[Dict[str, Any]]:
        """
        Extract tool calls from the LLM response.
        
        Args:
            response: The LLM response text
            
        Returns:
            List of tool call dictionaries
        """
        tools = []
        
        # Look for JSON tool calls in the response
        import re
        json_pattern = r'```json\s*(\{.*?\})\s*```'
        matches = re.findall(json_pattern, response, re.DOTALL)
        
        for match in matches:
            try:
                tool_call = json.loads(match)
                if 'tool_name' in tool_call or 'function' in tool_call:
                    tools.append(tool_call)
            except json.JSONDecodeError:
                continue
        
        return tools
    
    def _calculate_confidence_score(self, llm_response: Dict[str, Any]) -> float:
        """
        Calculate confidence score for the LLM response.
        
        Args:
            llm_response: The LLM response dictionary
            
        Returns:
            Confidence score between 0.0 and 1.0
        """
        # Simple confidence calculation based on response characteristics
        response_text = llm_response.get('response', '')
        
        # Base confidence
        confidence = 0.8
        
        # Adjust based on response length
        if len(response_text) < 10:
            confidence *= 0.7
        elif len(response_text) > 100:
            confidence *= 1.1
        
        # Adjust based on finish reason
        finish_reason = llm_response.get('finish_reason', 'stop')
        if finish_reason != 'stop':
            confidence *= 0.8
        
        # Adjust based on provider
        provider = llm_response.get('provider', '')
        if provider == 'local':
            confidence *= 0.9
        
        return min(confidence, 1.0)
    
    def get_available_providers(self) -> List[str]:
        """Get list of available provider names."""
        return [name for name, provider in self.providers.items() if provider.is_available()]
    
    def get_provider_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status information for all providers."""
        status = {}
        
        for name, provider in self.providers.items():
            status[name] = {
                'available': provider.is_available(),
                'model': getattr(provider, 'model', 'unknown'),
                'is_primary': name == self.primary_provider,
                'is_fallback': name in self.fallback_providers
            }
        
        return status
    
    def switch_primary_provider(self, provider_name: str) -> bool:
        """
        Switch the primary provider.
        
        Args:
            provider_name: Name of the provider to make primary
            
        Returns:
            True if switch was successful, False otherwise
        """
        if provider_name in self.providers and self.providers[provider_name].is_available():
            # Move current primary to fallback
            if self.primary_provider and self.primary_provider not in self.fallback_providers:
                self.fallback_providers.insert(0, self.primary_provider)
            
            # Remove new primary from fallback list
            if provider_name in self.fallback_providers:
                self.fallback_providers.remove(provider_name)
            
            self.primary_provider = provider_name
            logger.info(f"Switched primary LLM provider to: {provider_name}")
            return True
        
        return False
