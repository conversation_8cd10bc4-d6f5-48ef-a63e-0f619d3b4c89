"""
Services module for the AI agent system.

This module contains service layer components that provide business logic
and coordinate between different parts of the system. Services handle
complex operations, external integrations, and business rules.

Service categories:
- LLM integration services (OpenAI, Anthropic, local models)
- External API services (Calendar, Weather, etc.)
- User management services
- Conversation and message services
- Notification and reminder services
- Analytics and monitoring services

Services are designed to be stateless and can be easily tested
and mocked for unit testing purposes.
"""
