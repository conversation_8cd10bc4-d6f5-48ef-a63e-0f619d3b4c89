"""
Calendar service for managing calendar events and integrations.

This service provides functionality for creating, reading, updating, and
deleting calendar events across different calendar providers including
Google Calendar, Outlook, and local calendar storage.

Features:
- Multi-provider calendar support
- Event CRUD operations
- Calendar synchronization
- Recurring event management
- Timezone handling
- Conflict detection and resolution
- Calendar sharing and permissions

The service abstracts calendar provider differences and provides
a unified interface for calendar operations throughout the system.
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class CalendarEvent:
    """Represents a calendar event."""
    id: Optional[str] = None
    title: str = ""
    description: str = ""
    start_time: datetime = None
    end_time: datetime = None
    location: str = ""
    attendees: List[str] = None
    recurrence_rule: str = ""
    timezone: str = "UTC"
    provider: str = ""
    external_id: str = ""
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.attendees is None:
            self.attendees = []
        if self.metadata is None:
            self.metadata = {}


class CalendarProvider:
    """Base class for calendar providers."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.name = "base"
    
    async def create_event(self, event: CalendarEvent) -> CalendarEvent:
        """Create a new calendar event."""
        raise NotImplementedError("Subclasses must implement create_event")
    
    async def get_event(self, event_id: str) -> Optional[CalendarEvent]:
        """Get a calendar event by ID."""
        raise NotImplementedError("Subclasses must implement get_event")
    
    async def update_event(self, event: CalendarEvent) -> CalendarEvent:
        """Update an existing calendar event."""
        raise NotImplementedError("Subclasses must implement update_event")
    
    async def delete_event(self, event_id: str) -> bool:
        """Delete a calendar event."""
        raise NotImplementedError("Subclasses must implement delete_event")
    
    async def list_events(self, 
                         start_date: datetime = None,
                         end_date: datetime = None,
                         limit: int = 100) -> List[CalendarEvent]:
        """List calendar events within a date range."""
        raise NotImplementedError("Subclasses must implement list_events")
    
    async def search_events(self, query: str, limit: int = 50) -> List[CalendarEvent]:
        """Search for calendar events."""
        raise NotImplementedError("Subclasses must implement search_events")
    
    def is_available(self) -> bool:
        """Check if the provider is available."""
        return True


class GoogleCalendarProvider(CalendarProvider):
    """Google Calendar provider implementation."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.name = "google"
        self.api_key = config.get('api_key')
        self.calendar_id = config.get('calendar_id', 'primary')
    
    async def create_event(self, event: CalendarEvent) -> CalendarEvent:
        """Create event in Google Calendar."""
        try:
            # Simulate Google Calendar API call
            await asyncio.sleep(0.1)
            
            # Generate a mock event ID
            event.id = f"google_{datetime.now().timestamp()}"
            event.provider = self.name
            event.external_id = event.id
            
            logger.info(f"Created Google Calendar event: {event.title}")
            return event
            
        except Exception as e:
            logger.error(f"Google Calendar API error: {e}")
            raise
    
    async def get_event(self, event_id: str) -> Optional[CalendarEvent]:
        """Get event from Google Calendar."""
        try:
            # Simulate API call
            await asyncio.sleep(0.1)
            
            # Return mock event
            return CalendarEvent(
                id=event_id,
                title="Sample Google Event",
                description="This is a sample event from Google Calendar",
                start_time=datetime.now(),
                end_time=datetime.now() + timedelta(hours=1),
                provider=self.name
            )
            
        except Exception as e:
            logger.error(f"Google Calendar API error: {e}")
            return None
    
    async def update_event(self, event: CalendarEvent) -> CalendarEvent:
        """Update event in Google Calendar."""
        try:
            # Simulate API call
            await asyncio.sleep(0.1)
            
            event.provider = self.name
            logger.info(f"Updated Google Calendar event: {event.title}")
            return event
            
        except Exception as e:
            logger.error(f"Google Calendar API error: {e}")
            raise
    
    async def delete_event(self, event_id: str) -> bool:
        """Delete event from Google Calendar."""
        try:
            # Simulate API call
            await asyncio.sleep(0.1)
            
            logger.info(f"Deleted Google Calendar event: {event_id}")
            return True
            
        except Exception as e:
            logger.error(f"Google Calendar API error: {e}")
            return False
    
    async def list_events(self, 
                         start_date: datetime = None,
                         end_date: datetime = None,
                         limit: int = 100) -> List[CalendarEvent]:
        """List events from Google Calendar."""
        try:
            # Simulate API call
            await asyncio.sleep(0.1)
            
            # Return mock events
            events = []
            for i in range(min(3, limit)):  # Return up to 3 mock events
                events.append(CalendarEvent(
                    id=f"google_event_{i}",
                    title=f"Google Event {i+1}",
                    description=f"Description for event {i+1}",
                    start_time=datetime.now() + timedelta(days=i),
                    end_time=datetime.now() + timedelta(days=i, hours=1),
                    provider=self.name
                ))
            
            return events
            
        except Exception as e:
            logger.error(f"Google Calendar API error: {e}")
            return []
    
    async def search_events(self, query: str, limit: int = 50) -> List[CalendarEvent]:
        """Search events in Google Calendar."""
        try:
            # Simulate API call
            await asyncio.sleep(0.1)
            
            # Return mock search results
            return [CalendarEvent(
                id="google_search_result",
                title=f"Search result for: {query}",
                description="This is a search result from Google Calendar",
                start_time=datetime.now(),
                end_time=datetime.now() + timedelta(hours=1),
                provider=self.name
            )]
            
        except Exception as e:
            logger.error(f"Google Calendar API error: {e}")
            return []
    
    def is_available(self) -> bool:
        """Check if Google Calendar is available."""
        return bool(self.api_key)


class LocalCalendarProvider(CalendarProvider):
    """Local calendar provider implementation."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.name = "local"
        self._events: Dict[str, CalendarEvent] = {}
        self._next_id = 1
    
    async def create_event(self, event: CalendarEvent) -> CalendarEvent:
        """Create event in local storage."""
        event.id = f"local_{self._next_id}"
        event.provider = self.name
        event.external_id = event.id
        
        self._events[event.id] = event
        self._next_id += 1
        
        logger.info(f"Created local calendar event: {event.title}")
        return event
    
    async def get_event(self, event_id: str) -> Optional[CalendarEvent]:
        """Get event from local storage."""
        return self._events.get(event_id)
    
    async def update_event(self, event: CalendarEvent) -> CalendarEvent:
        """Update event in local storage."""
        if event.id and event.id in self._events:
            self._events[event.id] = event
            logger.info(f"Updated local calendar event: {event.title}")
        else:
            raise ValueError(f"Event not found: {event.id}")
        
        return event
    
    async def delete_event(self, event_id: str) -> bool:
        """Delete event from local storage."""
        if event_id in self._events:
            del self._events[event_id]
            logger.info(f"Deleted local calendar event: {event_id}")
            return True
        return False
    
    async def list_events(self, 
                         start_date: datetime = None,
                         end_date: datetime = None,
                         limit: int = 100) -> List[CalendarEvent]:
        """List events from local storage."""
        events = list(self._events.values())
        
        # Filter by date range if provided
        if start_date or end_date:
            filtered_events = []
            for event in events:
                if start_date and event.start_time < start_date:
                    continue
                if end_date and event.start_time > end_date:
                    continue
                filtered_events.append(event)
            events = filtered_events
        
        # Apply limit
        return events[:limit]
    
    async def search_events(self, query: str, limit: int = 50) -> List[CalendarEvent]:
        """Search events in local storage."""
        query_lower = query.lower()
        matching_events = []
        
        for event in self._events.values():
            if (query_lower in event.title.lower() or 
                query_lower in event.description.lower() or
                query_lower in event.location.lower()):
                matching_events.append(event)
                
                if len(matching_events) >= limit:
                    break
        
        return matching_events


class CalendarService:
    """
    Main calendar service that manages multiple providers and operations.
    
    Provides a unified interface for calendar operations with support
    for multiple calendar providers and intelligent provider selection.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the calendar service.
        
        Args:
            config: Configuration for calendar providers and service settings
        """
        self.config = config or {}
        self.providers: Dict[str, CalendarProvider] = {}
        self.primary_provider = None
        
        # Service configuration
        self.enable_sync = self.config.get('enable_sync', False)
        self.default_timezone = self.config.get('default_timezone', 'UTC')
        self.conflict_resolution = self.config.get('conflict_resolution', 'manual')
        
        # Initialize providers
        self._initialize_providers()
        
        logger.info(f"Calendar service initialized with {len(self.providers)} providers")
    
    def _initialize_providers(self) -> None:
        """Initialize calendar providers based on configuration."""
        provider_configs = self.config.get('providers', {})
        
        # Initialize Google Calendar provider
        if 'google' in provider_configs:
            self.providers['google'] = GoogleCalendarProvider(provider_configs['google'])
        
        # Initialize local provider (always available)
        self.providers['local'] = LocalCalendarProvider(provider_configs.get('local', {}))
        
        # Set primary provider
        provider_priority = self.config.get('provider_priority', ['google', 'local'])
        
        for provider_name in provider_priority:
            if provider_name in self.providers and self.providers[provider_name].is_available():
                self.primary_provider = provider_name
                break
        
        if not self.primary_provider:
            self.primary_provider = 'local'  # Fallback to local
    
    async def create_event(self, 
                          title: str,
                          start_time: datetime,
                          end_time: datetime = None,
                          description: str = "",
                          location: str = "",
                          attendees: List[str] = None,
                          provider: str = None) -> CalendarEvent:
        """
        Create a new calendar event.
        
        Args:
            title: Event title
            start_time: Event start time
            end_time: Event end time (defaults to 1 hour after start)
            description: Event description
            location: Event location
            attendees: List of attendee email addresses
            provider: Specific provider to use (optional)
            
        Returns:
            Created CalendarEvent
        """
        if end_time is None:
            end_time = start_time + timedelta(hours=1)
        
        event = CalendarEvent(
            title=title,
            description=description,
            start_time=start_time,
            end_time=end_time,
            location=location,
            attendees=attendees or [],
            timezone=self.default_timezone
        )
        
        # Select provider
        provider_name = provider or self.primary_provider
        if provider_name not in self.providers:
            raise ValueError(f"Unknown calendar provider: {provider_name}")
        
        calendar_provider = self.providers[provider_name]
        created_event = await calendar_provider.create_event(event)
        
        logger.info(f"Created calendar event '{title}' using {provider_name} provider")
        return created_event
    
    async def get_event(self, event_id: str, provider: str = None) -> Optional[CalendarEvent]:
        """
        Get a calendar event by ID.
        
        Args:
            event_id: Event ID to retrieve
            provider: Specific provider to search (optional)
            
        Returns:
            CalendarEvent if found, None otherwise
        """
        if provider:
            if provider in self.providers:
                return await self.providers[provider].get_event(event_id)
            else:
                raise ValueError(f"Unknown calendar provider: {provider}")
        
        # Search all providers
        for provider_name, calendar_provider in self.providers.items():
            event = await calendar_provider.get_event(event_id)
            if event:
                return event
        
        return None
    
    async def search_events(self, 
                           query: str,
                           start_date: datetime = None,
                           end_date: datetime = None,
                           limit: int = 50) -> List[CalendarEvent]:
        """
        Search for calendar events.
        
        Args:
            query: Search query
            start_date: Start date for search range (optional)
            end_date: End date for search range (optional)
            limit: Maximum number of results
            
        Returns:
            List of matching CalendarEvents
        """
        all_events = []
        
        # Search all providers
        for provider_name, calendar_provider in self.providers.items():
            try:
                events = await calendar_provider.search_events(query, limit)
                
                # Filter by date range if provided
                if start_date or end_date:
                    filtered_events = []
                    for event in events:
                        if start_date and event.start_time < start_date:
                            continue
                        if end_date and event.start_time > end_date:
                            continue
                        filtered_events.append(event)
                    events = filtered_events
                
                all_events.extend(events)
                
            except Exception as e:
                logger.warning(f"Search failed for provider {provider_name}: {e}")
        
        # Sort by start time and apply limit
        all_events.sort(key=lambda e: e.start_time)
        return all_events[:limit]
    
    async def get_upcoming_events(self, 
                                 days_ahead: int = 7,
                                 limit: int = 20) -> List[CalendarEvent]:
        """
        Get upcoming events within the specified number of days.
        
        Args:
            days_ahead: Number of days to look ahead
            limit: Maximum number of events to return
            
        Returns:
            List of upcoming CalendarEvents
        """
        start_date = datetime.now()
        end_date = start_date + timedelta(days=days_ahead)
        
        all_events = []
        
        # Get events from all providers
        for provider_name, calendar_provider in self.providers.items():
            try:
                events = await calendar_provider.list_events(start_date, end_date, limit)
                all_events.extend(events)
            except Exception as e:
                logger.warning(f"Failed to get events from provider {provider_name}: {e}")
        
        # Sort by start time and apply limit
        all_events.sort(key=lambda e: e.start_time)
        return all_events[:limit]
    
    def get_available_providers(self) -> List[str]:
        """Get list of available provider names."""
        return [name for name, provider in self.providers.items() if provider.is_available()]
    
    def get_provider_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status information for all providers."""
        status = {}
        
        for name, provider in self.providers.items():
            status[name] = {
                'available': provider.is_available(),
                'is_primary': name == self.primary_provider,
                'name': provider.name
            }
        
        return status
