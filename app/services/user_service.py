"""
Enhanced user service for the AI agent system.

This service extends the basic user service functionality to provide
comprehensive user management, session handling, and preference management
for the AI agent architecture. It integrates with the event bus system
and provides advanced user context management.

Features:
- User creation and management
- Session tracking and management
- User preference handling
- Authentication and authorization
- User analytics and insights
- Profile management
- Privacy and data handling

The service provides a centralized interface for all user-related
operations throughout the AI agent system.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from aiogram.types import User as TelegramUser, Message as TelegramMessage

from ..model.user import User, UserData
from ..model.conversation import Conversation, ConversationData
from ..utils.events import UserSessionStarted, UserSessionEnded
from ..utils.event_bus import event_bus
from ..utils.constants import Platform, Language, TimeZone

logger = logging.getLogger(__name__)


class UserService:
    """
    Enhanced user service for comprehensive user management.
    
    Provides advanced user management capabilities including session
    tracking, preference management, and integration with the event system.
    """
    
    def __init__(self, user_repository=None, conversation_repository=None):
        """
        Initialize the user service.
        
        Args:
            user_repository: Repository for user data operations
            conversation_repository: Repository for conversation data operations
        """
        # In a complete implementation, these would be injected dependencies
        self.user_repository = user_repository
        self.conversation_repository = conversation_repository
        
        # Session tracking (in production, use Redis or database)
        self._active_sessions: Dict[str, Dict[str, Any]] = {}
        self._user_cache: Dict[int, User] = {}
        
        # Service configuration
        self.session_timeout_minutes = 30
        self.enable_analytics = True
        self.enable_caching = True
        
        logger.info("Enhanced user service initialized")
    
    async def identify_user_from_message(self, message: TelegramMessage) -> Optional[Dict[str, Any]]:
        """
        Identify user from a Telegram message and load comprehensive context.
        
        Args:
            message: The incoming Telegram message
            
        Returns:
            Dictionary containing user information and context, or None if identification fails
        """
        if not message or not message.from_user:
            logger.warning("Cannot identify user: message or user information missing")
            return None
        
        telegram_user = message.from_user
        
        try:
            # Load or create user
            user = await self._load_or_create_user(telegram_user)
            if not user:
                logger.error(f"Failed to load or create user for telegram_id: {telegram_user.id}")
                return None
            
            # Load conversation context
            conversation = await self._load_or_create_conversation(user.id, message.chat.id)
            
            # Update session information
            session_info = await self._update_user_session(user.id, message.chat.id, telegram_user)
            
            # Load user preferences and settings
            preferences = await self._load_user_preferences(user.id)
            
            # Prepare comprehensive user context
            user_context = {
                'user': user,
                'conversation': conversation,
                'session': session_info,
                'preferences': preferences,
                'telegram_user': telegram_user,
                'platform_metadata': {
                    'chat_id': message.chat.id,
                    'message_id': message.message_id,
                    'chat_type': message.chat.type,
                    'timestamp': datetime.now(),
                    'platform': Platform.TELEGRAM.value
                },
                'user_info': {
                    'user_id': user.id,
                    'telegram_id': user.telegram_id,
                    'username': user.username,
                    'first_name': user.first_name,
                    'language_code': user.language_code,
                    'timezone': user.timezone,
                    'is_active': user.is_active
                }
            }
            
            # Update user interaction timestamp
            await self._update_last_interaction(user.id)
            
            logger.info(f"Successfully identified user {user.id} from message")
            return user_context
            
        except Exception as e:
            logger.error(f"Error identifying user from message: {e}", exc_info=True)
            return None
    
    async def _load_or_create_user(self, telegram_user: TelegramUser) -> Optional[User]:
        """
        Load existing user or create new user from Telegram user data.
        
        Args:
            telegram_user: Telegram user object
            
        Returns:
            User model instance or None if creation fails
        """
        try:
            # Check cache first
            if self.enable_caching and telegram_user.id in self._user_cache:
                cached_user = self._user_cache[telegram_user.id]
                # Check if cache is still valid (less than 5 minutes old)
                if hasattr(cached_user, '_cache_time'):
                    cache_age = datetime.now() - cached_user._cache_time
                    if cache_age < timedelta(minutes=5):
                        return cached_user
            
            # In a complete implementation, this would query the database
            # For now, create a new user instance with enhanced data
            user_data = UserData(
                telegram_id=telegram_user.id,
                username=telegram_user.username,
                first_name=telegram_user.first_name or "",
                last_name=telegram_user.last_name or "",
                language_code=telegram_user.language_code or "en",
                is_active=True,
                last_interaction=datetime.now(),
                timezone=self._detect_user_timezone(telegram_user),
                notification_preferences=self._get_default_notification_preferences(),
                has_google_auth=False
            )
            
            user = User(**user_data.dict())
            
            # Add to cache
            if self.enable_caching:
                user._cache_time = datetime.now()
                self._user_cache[telegram_user.id] = user
            
            logger.debug(f"Created/loaded user: {user.telegram_id}")
            return user
            
        except Exception as e:
            logger.error(f"Error loading/creating user: {e}", exc_info=True)
            return None
    
    async def _load_or_create_conversation(self, user_id: int, chat_id: int) -> Optional[Conversation]:
        """
        Load existing conversation or create new conversation.
        
        Args:
            user_id: Internal user ID
            chat_id: Telegram chat ID
            
        Returns:
            Conversation model instance or None if creation fails
        """
        try:
            # In a complete implementation, this would query the database
            # For now, create a new conversation instance
            conversation_data = ConversationData(
                user_id=user_id,
                telegram_chat_id=chat_id,
                title=f"Chat {chat_id}",
                context=self._get_default_conversation_context(),
                is_active=True,
                last_message_at=datetime.now()
            )
            
            conversation = Conversation(**conversation_data.dict())
            
            logger.debug(f"Created/loaded conversation for user {user_id}, chat {chat_id}")
            return conversation
            
        except Exception as e:
            logger.error(f"Error loading/creating conversation: {e}", exc_info=True)
            return None
    
    async def _update_user_session(self, user_id: int, chat_id: int, telegram_user: TelegramUser) -> Dict[str, Any]:
        """
        Update user session information and publish session events.
        
        Args:
            user_id: Internal user ID
            chat_id: Telegram chat ID
            telegram_user: Telegram user object
            
        Returns:
            Session information dictionary
        """
        session_key = f"{user_id}_{chat_id}"
        now = datetime.now()
        
        # Check if this is a new session
        is_new_session = session_key not in self._active_sessions
        
        if is_new_session:
            # Start new session
            session_info = {
                'user_id': user_id,
                'chat_id': chat_id,
                'session_start': now,
                'last_activity': now,
                'message_count': 1,
                'platform': Platform.TELEGRAM.value,
                'session_id': f"session_{user_id}_{int(now.timestamp())}",
                'user_agent': 'Telegram',
                'ip_address': None,  # Not available in Telegram
                'features_used': [],
                'errors_encountered': 0
            }
            
            self._active_sessions[session_key] = session_info
            
            # Publish session started event
            await event_bus.publish(UserSessionStarted(
                user_id=user_id,
                chat_id=chat_id,
                platform=Platform.TELEGRAM.value,
                user_metadata={
                    'username': telegram_user.username,
                    'first_name': telegram_user.first_name,
                    'language_code': telegram_user.language_code,
                    'session_id': session_info['session_id']
                }
            ))
            
            logger.info(f"Started new session for user {user_id}")
            
        else:
            # Update existing session
            session_info = self._active_sessions[session_key]
            session_info['last_activity'] = now
            session_info['message_count'] += 1
            
            logger.debug(f"Updated session for user {user_id}")
        
        return session_info.copy()
    
    async def _load_user_preferences(self, user_id: int) -> Dict[str, Any]:
        """
        Load user preferences and settings.
        
        Args:
            user_id: Internal user ID
            
        Returns:
            User preferences dictionary
        """
        # In a complete implementation, this would load from database
        # For now, return default preferences
        return {
            'language': Language.ENGLISH.value,
            'timezone': TimeZone.UTC.value,
            'notifications': {
                'event_reminders': True,
                'daily_summary': False,
                'bot_updates': True,
                'marketing': False
            },
            'calendar': {
                'default_duration_minutes': 60,
                'default_reminder_minutes': 15,
                'working_hours_start': '09:00',
                'working_hours_end': '17:00',
                'working_days': ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
            },
            'privacy': {
                'share_analytics': True,
                'store_conversation_history': True,
                'allow_ai_training': False
            },
            'ui': {
                'theme': 'auto',
                'compact_mode': False,
                'show_advanced_features': False
            }
        }
    
    def _detect_user_timezone(self, telegram_user: TelegramUser) -> str:
        """
        Detect user timezone based on available information.
        
        Args:
            telegram_user: Telegram user object
            
        Returns:
            Timezone string
        """
        # In a complete implementation, this would use more sophisticated detection
        # For now, return UTC as default
        language_timezone_map = {
            'en': TimeZone.UTC.value,
            'de': TimeZone.EUROPE_BERLIN.value,
            'fr': TimeZone.EUROPE_PARIS.value,
            'es': TimeZone.EUROPE_PARIS.value,  # Assuming Spain
            'ja': TimeZone.ASIA_TOKYO.value,
            'zh': TimeZone.ASIA_SHANGHAI.value
        }
        
        language_code = telegram_user.language_code or 'en'
        return language_timezone_map.get(language_code, TimeZone.UTC.value)
    
    def _get_default_notification_preferences(self) -> Dict[str, bool]:
        """Get default notification preferences for new users."""
        return {
            "event_reminder": True,
            "daily_summary": False,
            "bot_updates": False,
            "marketing": False,
            "security_alerts": True
        }
    
    def _get_default_conversation_context(self) -> Dict[str, Any]:
        """Get default conversation context for new conversations."""
        return {
            'conversation_type': 'personal_assistant',
            'topics_discussed': [],
            'user_goals': [],
            'preferences_learned': {},
            'last_context_update': datetime.now().isoformat()
        }
    
    async def _update_last_interaction(self, user_id: int) -> None:
        """
        Update the user's last interaction timestamp.
        
        Args:
            user_id: Internal user ID
        """
        # In a complete implementation, this would update the database
        # For now, update the cache if available
        if self.enable_caching:
            for cached_user in self._user_cache.values():
                if hasattr(cached_user, 'id') and cached_user.id == user_id:
                    cached_user.last_interaction = datetime.now()
                    break
    
    async def end_user_session(self, user_id: int, chat_id: int) -> None:
        """
        End a user session and publish session ended event.
        
        Args:
            user_id: Internal user ID
            chat_id: Telegram chat ID
        """
        session_key = f"{user_id}_{chat_id}"
        
        if session_key in self._active_sessions:
            session_info = self._active_sessions.pop(session_key)
            
            # Calculate session duration
            session_duration = datetime.now() - session_info['session_start']
            duration_ms = int(session_duration.total_seconds() * 1000)
            
            # Publish session ended event
            await event_bus.publish(UserSessionEnded(
                user_id=user_id,
                chat_id=chat_id,
                session_duration_ms=duration_ms,
                messages_processed=session_info['message_count'],
                metadata={
                    'session_id': session_info.get('session_id'),
                    'features_used': session_info.get('features_used', []),
                    'errors_encountered': session_info.get('errors_encountered', 0)
                }
            ))
            
            logger.info(f"Ended session for user {user_id}, duration: {duration_ms}ms")
    
    def get_user_display_name(self, telegram_user: TelegramUser) -> str:
        """
        Get a display name for the user.
        
        Args:
            telegram_user: Telegram user object
            
        Returns:
            User's display name
        """
        if telegram_user.first_name:
            return telegram_user.first_name
        elif telegram_user.username:
            return telegram_user.username
        else:
            return "User"
    
    def format_greeting_message(self, telegram_user: TelegramUser) -> str:
        """
        Format a greeting message for the user.
        
        Args:
            telegram_user: Telegram user object
            
        Returns:
            Formatted greeting message
        """
        display_name = self.get_user_display_name(telegram_user)
        return f"Hello {display_name}"
    
    async def update_user_preferences(self, user_id: int, preferences: Dict[str, Any]) -> bool:
        """
        Update user preferences.
        
        Args:
            user_id: Internal user ID
            preferences: Preferences to update
            
        Returns:
            True if update was successful, False otherwise
        """
        try:
            # In a complete implementation, this would update the database
            logger.info(f"Updated preferences for user {user_id}: {list(preferences.keys())}")
            return True
        except Exception as e:
            logger.error(f"Error updating user preferences: {e}", exc_info=True)
            return False
    
    async def get_user_analytics(self, user_id: int) -> Dict[str, Any]:
        """
        Get analytics data for a user.
        
        Args:
            user_id: Internal user ID
            
        Returns:
            User analytics dictionary
        """
        if not self.enable_analytics:
            return {}
        
        # In a complete implementation, this would query analytics database
        return {
            'total_messages': 0,
            'total_sessions': 0,
            'average_session_duration_minutes': 0,
            'most_used_features': [],
            'preferred_interaction_times': [],
            'satisfaction_score': 0.0,
            'last_activity': None
        }
    
    def get_active_sessions(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all active sessions."""
        return self._active_sessions.copy()
    
    def clear_inactive_sessions(self, timeout_minutes: int = None) -> int:
        """
        Clear sessions that have been inactive for the specified timeout.
        
        Args:
            timeout_minutes: Minutes of inactivity before session is considered expired
            
        Returns:
            Number of sessions cleared
        """
        if timeout_minutes is None:
            timeout_minutes = self.session_timeout_minutes
        
        now = datetime.now()
        expired_sessions = []
        
        for session_key, session_info in self._active_sessions.items():
            inactive_duration = now - session_info['last_activity']
            if inactive_duration.total_seconds() > (timeout_minutes * 60):
                expired_sessions.append(session_key)
        
        # Remove expired sessions
        for session_key in expired_sessions:
            del self._active_sessions[session_key]
        
        if expired_sessions:
            logger.info(f"Cleared {len(expired_sessions)} inactive sessions")
        
        return len(expired_sessions)
    
    def clear_user_cache(self) -> None:
        """Clear the user cache."""
        self._user_cache.clear()
        logger.info("Cleared user cache")
    
    def get_service_stats(self) -> Dict[str, Any]:
        """Get service statistics."""
        return {
            'active_sessions': len(self._active_sessions),
            'cached_users': len(self._user_cache),
            'session_timeout_minutes': self.session_timeout_minutes,
            'analytics_enabled': self.enable_analytics,
            'caching_enabled': self.enable_caching
        }
