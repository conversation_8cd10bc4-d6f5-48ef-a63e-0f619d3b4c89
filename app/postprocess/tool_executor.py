"""
Tool execution component for postprocessing pipeline.

This module handles the execution of tools requested by the LLM in its response.
It provides a secure and controlled environment for tool execution with proper
error handling, logging, and result validation.

Supported tool categories:
- Calendar management tools (create, search, update events)
- Reminder and notification tools
- External API integration tools
- Data retrieval and processing tools
- User preference management tools

The executor ensures that tools are executed safely with proper validation
of parameters and results, and provides comprehensive logging for debugging.
"""

import json
import logging
import asyncio
from typing import Dict, Any, List, Optional, Callable, Union
from dataclasses import dataclass
from datetime import datetime, timedelta
import uuid

logger = logging.getLogger(__name__)


@dataclass
class ToolExecutionResult:
    """Result of tool execution."""
    success: bool
    result: Any = None
    error_message: Optional[str] = None
    execution_time_ms: Optional[int] = None
    metadata: Dict[str, Any] = None


@dataclass
class ToolDefinition:
    """Definition of an available tool."""
    name: str
    description: str
    parameters: Dict[str, Any]
    handler: Callable
    requires_auth: bool = False
    timeout_seconds: int = 30
    rate_limit_per_minute: int = 10


class ToolExecutor:
    """
    Executes tools requested by the LLM with proper validation and error handling.
    
    Provides a secure environment for tool execution with rate limiting,
    timeout handling, and comprehensive result validation.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the tool executor.
        
        Args:
            config: Configuration dictionary for tool execution
        """
        # Default configuration
        self.config = {
            'max_concurrent_executions': 5,
            'default_timeout_seconds': 30,
            'enable_rate_limiting': True,
            'max_execution_time_ms': 60000,
            'allow_parallel_execution': True,
            'log_tool_usage': True
        }
        
        if config:
            self.config.update(config)
        
        # Tool registry
        self._tools: Dict[str, ToolDefinition] = {}
        
        # Execution tracking
        self._active_executions: Dict[str, Dict[str, Any]] = {}
        self._execution_history: List[Dict[str, Any]] = []
        
        # Rate limiting (in production, use Redis or database)
        self._rate_limit_storage: Dict[str, List[datetime]] = {}
        
        # Register built-in tools
        self._register_builtin_tools()
        
        logger.info("Tool executor initialized")
    
    def register_tool(self, tool_definition: ToolDefinition) -> None:
        """
        Register a new tool for execution.
        
        Args:
            tool_definition: Definition of the tool to register
        """
        self._tools[tool_definition.name] = tool_definition
        logger.info(f"Registered tool: {tool_definition.name}")
    
    async def execute_tools(self, 
                          tool_calls: List[Dict[str, Any]], 
                          user_context: Dict[str, Any]) -> List[ToolExecutionResult]:
        """
        Execute multiple tools from LLM response.
        
        Args:
            tool_calls: List of tool calls to execute
            user_context: User context for authentication and personalization
            
        Returns:
            List of execution results for each tool call
        """
        if not tool_calls:
            return []
        
        logger.info(f"Executing {len(tool_calls)} tool calls")
        
        results = []
        
        if self.config['allow_parallel_execution'] and len(tool_calls) > 1:
            # Execute tools in parallel
            tasks = []
            for tool_call in tool_calls:
                task = asyncio.create_task(self._execute_single_tool(tool_call, user_context))
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Convert exceptions to error results
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    results[i] = ToolExecutionResult(
                        success=False,
                        error_message=f"Tool execution failed: {str(result)}",
                        metadata={'tool_call': tool_calls[i]}
                    )
        else:
            # Execute tools sequentially
            for tool_call in tool_calls:
                result = await self._execute_single_tool(tool_call, user_context)
                results.append(result)
        
        logger.info(f"Completed execution of {len(tool_calls)} tools")
        return results
    
    async def _execute_single_tool(self, 
                                 tool_call: Dict[str, Any], 
                                 user_context: Dict[str, Any]) -> ToolExecutionResult:
        """
        Execute a single tool call.
        
        Args:
            tool_call: Tool call definition
            user_context: User context for execution
            
        Returns:
            ToolExecutionResult with execution status and results
        """
        execution_id = str(uuid.uuid4())
        start_time = datetime.now()
        
        try:
            # Extract tool information
            tool_name = tool_call.get('tool_name') or tool_call.get('function')
            parameters = tool_call.get('parameters', {})
            
            if not tool_name:
                return ToolExecutionResult(
                    success=False,
                    error_message="Tool name not specified in tool call",
                    metadata={'tool_call': tool_call}
                )
            
            # Validate tool exists
            if tool_name not in self._tools:
                return ToolExecutionResult(
                    success=False,
                    error_message=f"Unknown tool: {tool_name}",
                    metadata={'tool_call': tool_call, 'available_tools': list(self._tools.keys())}
                )
            
            tool_def = self._tools[tool_name]
            
            # Check rate limiting
            if not self._check_rate_limit(tool_name, user_context):
                return ToolExecutionResult(
                    success=False,
                    error_message=f"Rate limit exceeded for tool: {tool_name}",
                    metadata={'tool_call': tool_call}
                )
            
            # Validate parameters
            validation_result = self._validate_parameters(parameters, tool_def.parameters)
            if not validation_result[0]:
                return ToolExecutionResult(
                    success=False,
                    error_message=f"Parameter validation failed: {validation_result[1]}",
                    metadata={'tool_call': tool_call}
                )
            
            # Track execution
            self._active_executions[execution_id] = {
                'tool_name': tool_name,
                'start_time': start_time,
                'user_id': user_context.get('user_info', {}).get('user_id'),
                'parameters': parameters
            }
            
            # Execute tool with timeout
            try:
                result = await asyncio.wait_for(
                    tool_def.handler(parameters, user_context),
                    timeout=tool_def.timeout_seconds
                )
                
                execution_time = (datetime.now() - start_time).total_seconds() * 1000
                
                # Log successful execution
                if self.config['log_tool_usage']:
                    self._log_tool_execution(tool_name, parameters, result, execution_time, True)
                
                return ToolExecutionResult(
                    success=True,
                    result=result,
                    execution_time_ms=int(execution_time),
                    metadata={
                        'tool_name': tool_name,
                        'execution_id': execution_id,
                        'parameters': parameters
                    }
                )
                
            except asyncio.TimeoutError:
                execution_time = (datetime.now() - start_time).total_seconds() * 1000
                error_msg = f"Tool execution timed out after {tool_def.timeout_seconds} seconds"
                
                if self.config['log_tool_usage']:
                    self._log_tool_execution(tool_name, parameters, None, execution_time, False, error_msg)
                
                return ToolExecutionResult(
                    success=False,
                    error_message=error_msg,
                    execution_time_ms=int(execution_time),
                    metadata={'tool_call': tool_call, 'timeout': True}
                )
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            error_msg = f"Tool execution error: {str(e)}"
            
            logger.error(f"Error executing tool {tool_name}: {e}", exc_info=True)
            
            if self.config['log_tool_usage']:
                self._log_tool_execution(tool_name, parameters, None, execution_time, False, error_msg)
            
            return ToolExecutionResult(
                success=False,
                error_message=error_msg,
                execution_time_ms=int(execution_time),
                metadata={'tool_call': tool_call, 'exception': True}
            )
        
        finally:
            # Clean up execution tracking
            if execution_id in self._active_executions:
                del self._active_executions[execution_id]
    
    def _check_rate_limit(self, tool_name: str, user_context: Dict[str, Any]) -> bool:
        """Check if tool execution is within rate limits."""
        if not self.config['enable_rate_limiting']:
            return True
        
        user_id = user_context.get('user_info', {}).get('user_id', 'unknown')
        rate_key = f"{user_id}_{tool_name}"
        
        now = datetime.now()
        window_start = now - timedelta(minutes=1)
        
        # Get tool's rate limit
        tool_def = self._tools.get(tool_name)
        if not tool_def:
            return False
        
        rate_limit = tool_def.rate_limit_per_minute
        
        # Check usage in the last minute
        if rate_key not in self._rate_limit_storage:
            self._rate_limit_storage[rate_key] = []
        
        usage_times = self._rate_limit_storage[rate_key]
        
        # Remove old entries
        usage_times[:] = [usage_time for usage_time in usage_times if usage_time > window_start]
        
        # Check if under limit
        if len(usage_times) >= rate_limit:
            return False
        
        # Add current usage
        usage_times.append(now)
        return True
    
    def _validate_parameters(self, parameters: Dict[str, Any], schema: Dict[str, Any]) -> tuple[bool, str]:
        """Validate tool parameters against schema."""
        try:
            # Basic validation - in production, use a proper schema validator like jsonschema
            required_params = schema.get('required', [])
            
            # Check required parameters
            for param in required_params:
                if param not in parameters:
                    return False, f"Missing required parameter: {param}"
            
            # Check parameter types (basic implementation)
            param_types = schema.get('properties', {})
            for param_name, param_value in parameters.items():
                if param_name in param_types:
                    expected_type = param_types[param_name].get('type')
                    if expected_type and not self._check_parameter_type(param_value, expected_type):
                        return False, f"Parameter {param_name} has incorrect type. Expected: {expected_type}"
            
            return True, ""
            
        except Exception as e:
            return False, f"Parameter validation error: {str(e)}"
    
    def _check_parameter_type(self, value: Any, expected_type: str) -> bool:
        """Check if parameter value matches expected type."""
        type_mapping = {
            'string': str,
            'integer': int,
            'number': (int, float),
            'boolean': bool,
            'array': list,
            'object': dict
        }
        
        expected_python_type = type_mapping.get(expected_type)
        if expected_python_type:
            return isinstance(value, expected_python_type)
        
        return True  # Unknown type, allow it
    
    def _log_tool_execution(self, 
                          tool_name: str, 
                          parameters: Dict[str, Any], 
                          result: Any, 
                          execution_time_ms: float, 
                          success: bool, 
                          error_message: str = None) -> None:
        """Log tool execution for monitoring and debugging."""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'tool_name': tool_name,
            'parameters': parameters,
            'success': success,
            'execution_time_ms': execution_time_ms,
            'result_type': type(result).__name__ if result is not None else None,
            'error_message': error_message
        }
        
        self._execution_history.append(log_entry)
        
        # Keep only last 100 entries (in production, use proper logging/monitoring)
        if len(self._execution_history) > 100:
            self._execution_history = self._execution_history[-100:]
        
        # Log to standard logger
        if success:
            logger.info(f"Tool executed successfully: {tool_name} ({execution_time_ms:.2f}ms)")
        else:
            logger.warning(f"Tool execution failed: {tool_name} - {error_message}")
    
    def _register_builtin_tools(self) -> None:
        """Register built-in tools."""
        
        # Calendar event creation tool
        self.register_tool(ToolDefinition(
            name="create_calendar_event",
            description="Create a new calendar event",
            parameters={
                "required": ["title", "start_time"],
                "properties": {
                    "title": {"type": "string"},
                    "start_time": {"type": "string"},
                    "end_time": {"type": "string"},
                    "description": {"type": "string"},
                    "location": {"type": "string"}
                }
            },
            handler=self._handle_create_calendar_event,
            timeout_seconds=15
        ))
        
        # Calendar search tool
        self.register_tool(ToolDefinition(
            name="search_calendar_events",
            description="Search for calendar events",
            parameters={
                "required": ["query"],
                "properties": {
                    "query": {"type": "string"},
                    "start_date": {"type": "string"},
                    "end_date": {"type": "string"},
                    "limit": {"type": "integer"}
                }
            },
            handler=self._handle_search_calendar_events,
            timeout_seconds=10
        ))
        
        # Reminder setting tool
        self.register_tool(ToolDefinition(
            name="set_reminder",
            description="Set a reminder for the user",
            parameters={
                "required": ["message", "reminder_time"],
                "properties": {
                    "message": {"type": "string"},
                    "reminder_time": {"type": "string"},
                    "repeat": {"type": "string"}
                }
            },
            handler=self._handle_set_reminder,
            timeout_seconds=5
        ))
    
    async def _handle_create_calendar_event(self, parameters: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle calendar event creation."""
        # Placeholder implementation - in production, integrate with actual calendar service
        await asyncio.sleep(0.1)  # Simulate API call
        
        event_id = str(uuid.uuid4())
        
        return {
            "event_id": event_id,
            "title": parameters["title"],
            "start_time": parameters["start_time"],
            "end_time": parameters.get("end_time"),
            "status": "created",
            "message": f"Calendar event '{parameters['title']}' created successfully"
        }
    
    async def _handle_search_calendar_events(self, parameters: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle calendar event search."""
        # Placeholder implementation
        await asyncio.sleep(0.1)  # Simulate API call
        
        return {
            "query": parameters["query"],
            "events": [],  # In production, return actual search results
            "total_count": 0,
            "message": f"Search completed for: {parameters['query']}"
        }
    
    async def _handle_set_reminder(self, parameters: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle reminder setting."""
        # Placeholder implementation
        await asyncio.sleep(0.1)  # Simulate processing
        
        reminder_id = str(uuid.uuid4())
        
        return {
            "reminder_id": reminder_id,
            "message": parameters["message"],
            "reminder_time": parameters["reminder_time"],
            "status": "scheduled",
            "confirmation": f"Reminder set: {parameters['message']}"
        }
    
    def get_available_tools(self) -> List[Dict[str, Any]]:
        """Get list of available tools."""
        return [
            {
                "name": tool_def.name,
                "description": tool_def.description,
                "parameters": tool_def.parameters,
                "requires_auth": tool_def.requires_auth,
                "timeout_seconds": tool_def.timeout_seconds
            }
            for tool_def in self._tools.values()
        ]
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """Get tool execution statistics."""
        total_executions = len(self._execution_history)
        successful_executions = sum(1 for entry in self._execution_history if entry['success'])
        
        return {
            "total_executions": total_executions,
            "successful_executions": successful_executions,
            "success_rate": successful_executions / total_executions if total_executions > 0 else 0,
            "active_executions": len(self._active_executions),
            "available_tools": len(self._tools),
            "last_execution": self._execution_history[-1] if self._execution_history else None
        }
    
    def clear_execution_history(self) -> None:
        """Clear execution history (useful for testing)."""
        self._execution_history.clear()
        self._rate_limit_storage.clear()
        logger.info("Cleared tool execution history")
