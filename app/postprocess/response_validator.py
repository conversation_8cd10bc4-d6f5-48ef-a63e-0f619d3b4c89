"""
Response validation component for postprocessing pipeline.

This module validates LLM responses to ensure they meet quality standards
and are safe for delivery to users. It checks for various issues including
content safety, response format, and appropriateness.

Validation includes:
- Content safety and appropriateness checks
- Response format and structure validation
- Length and quality assessments
- Tool usage validation
- Consistency checks with user request
- Language and tone appropriateness

The validator can reject responses, request regeneration, or apply
corrections based on configurable rules and policies.
"""

import re
import json
import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class ResponseValidationResult:
    """Result of LLM response validation."""
    is_valid: bool
    confidence_score: float = 0.0
    validation_errors: List[str] = None
    warnings: List[str] = None
    suggested_corrections: List[str] = None
    metadata: Dict[str, Any] = None


class ResponseValidator:
    """
    Validates LLM responses for quality, safety, and appropriateness.
    
    Ensures that responses meet standards before being sent to users
    and provides feedback for response improvement.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the response validator.
        
        Args:
            config: Configuration dictionary for validation rules
        """
        # Default configuration
        self.config = {
            'max_response_length': 2000,
            'min_response_length': 5,
            'require_helpful_response': True,
            'check_content_safety': True,
            'validate_tool_usage': True,
            'check_response_relevance': True,
            'allowed_languages': ['en', 'de', 'es', 'fr'],
            'forbidden_topics': ['illegal_activities', 'harmful_content'],
            'quality_threshold': 0.7,
            'require_json_for_tools': True
        }
        
        if config:
            self.config.update(config)
        
        # Compile regex patterns for efficiency
        self._tool_call_pattern = re.compile(r'```json\s*(\{.*?\})\s*```', re.DOTALL)
        self._harmful_content_patterns = [
            re.compile(r'\b(hate|violence|illegal)\b', re.IGNORECASE),
            # Add more patterns as needed
        ]
    
    async def validate_response(self, 
                              llm_response: str, 
                              original_prompt: str,
                              user_context: Dict[str, Any],
                              expected_tools: List[str] = None) -> ResponseValidationResult:
        """
        Validate an LLM response comprehensively.
        
        Args:
            llm_response: The response from the LLM to validate
            original_prompt: The original prompt sent to the LLM
            user_context: Context about the user and conversation
            expected_tools: List of tools that might be used in the response
            
        Returns:
            ResponseValidationResult with validation status and details
        """
        errors = []
        warnings = []
        suggestions = []
        confidence_score = 1.0
        
        try:
            # Basic validation checks
            basic_validation = self._validate_basic_requirements(llm_response)
            if not basic_validation[0]:
                errors.extend(basic_validation[1])
                confidence_score *= 0.5
            
            # Content safety validation
            safety_validation = self._validate_content_safety(llm_response)
            if not safety_validation[0]:
                errors.extend(safety_validation[1])
                confidence_score *= 0.3
            
            # Response relevance validation
            relevance_validation = self._validate_response_relevance(llm_response, original_prompt)
            if not relevance_validation[0]:
                warnings.extend(relevance_validation[1])
                confidence_score *= 0.8
            
            # Tool usage validation
            if expected_tools:
                tool_validation = self._validate_tool_usage(llm_response, expected_tools)
                if not tool_validation[0]:
                    warnings.extend(tool_validation[1])
                    confidence_score *= 0.9
            
            # Quality assessment
            quality_score = self._assess_response_quality(llm_response, original_prompt)
            confidence_score *= quality_score
            
            if quality_score < self.config['quality_threshold']:
                suggestions.append(f"Response quality score ({quality_score:.2f}) below threshold ({self.config['quality_threshold']})")
            
            # Language and tone validation
            language_validation = self._validate_language_and_tone(llm_response, user_context)
            if not language_validation[0]:
                warnings.extend(language_validation[1])
            
            # Final validation decision
            is_valid = len(errors) == 0 and confidence_score >= self.config['quality_threshold']
            
            return ResponseValidationResult(
                is_valid=is_valid,
                confidence_score=confidence_score,
                validation_errors=errors if errors else None,
                warnings=warnings if warnings else None,
                suggested_corrections=suggestions if suggestions else None,
                metadata={
                    'response_length': len(llm_response),
                    'quality_score': quality_score,
                    'validation_timestamp': datetime.now().isoformat(),
                    'validation_version': '1.0'
                }
            )
            
        except Exception as e:
            logger.error(f"Error during response validation: {e}", exc_info=True)
            return ResponseValidationResult(
                is_valid=False,
                confidence_score=0.0,
                validation_errors=[f"Validation error: {str(e)}"],
                metadata={'error_occurred': True}
            )
    
    def _validate_basic_requirements(self, response: str) -> Tuple[bool, List[str]]:
        """Validate basic response requirements."""
        errors = []
        
        # Check response length
        if len(response) < self.config['min_response_length']:
            errors.append(f"Response too short (minimum {self.config['min_response_length']} characters)")
        
        if len(response) > self.config['max_response_length']:
            errors.append(f"Response too long (maximum {self.config['max_response_length']} characters)")
        
        # Check for empty or whitespace-only response
        if not response.strip():
            errors.append("Response is empty or contains only whitespace")
        
        # Check for helpful content requirement
        if self.config['require_helpful_response']:
            if self._is_unhelpful_response(response):
                errors.append("Response appears to be unhelpful or dismissive")
        
        return len(errors) == 0, errors
    
    def _validate_content_safety(self, response: str) -> Tuple[bool, List[str]]:
        """Validate response content for safety."""
        if not self.config['check_content_safety']:
            return True, []
        
        errors = []
        
        # Check for harmful content patterns
        for pattern in self._harmful_content_patterns:
            if pattern.search(response):
                errors.append(f"Response contains potentially harmful content: {pattern.pattern}")
        
        # Check for forbidden topics
        response_lower = response.lower()
        for topic in self.config['forbidden_topics']:
            if topic.replace('_', ' ') in response_lower:
                errors.append(f"Response discusses forbidden topic: {topic}")
        
        # Check for inappropriate language
        if self._contains_inappropriate_language(response):
            errors.append("Response contains inappropriate language")
        
        return len(errors) == 0, errors
    
    def _validate_response_relevance(self, response: str, original_prompt: str) -> Tuple[bool, List[str]]:
        """Validate that the response is relevant to the original prompt."""
        if not self.config['check_response_relevance']:
            return True, []
        
        warnings = []
        
        # Extract key terms from prompt
        prompt_keywords = self._extract_keywords(original_prompt)
        response_keywords = self._extract_keywords(response)
        
        # Check for keyword overlap
        if prompt_keywords and response_keywords:
            overlap = set(prompt_keywords) & set(response_keywords)
            relevance_ratio = len(overlap) / len(prompt_keywords) if prompt_keywords else 0
            
            if relevance_ratio < 0.3:  # Less than 30% keyword overlap
                warnings.append(f"Low relevance to original request (relevance: {relevance_ratio:.2f})")
        
        # Check for generic responses
        if self._is_generic_response(response):
            warnings.append("Response appears to be generic and not tailored to the specific request")
        
        return len(warnings) == 0, warnings
    
    def _validate_tool_usage(self, response: str, expected_tools: List[str]) -> Tuple[bool, List[str]]:
        """Validate tool usage in the response."""
        if not self.config['validate_tool_usage']:
            return True, []
        
        warnings = []
        
        # Extract tool calls from response
        tool_calls = self._extract_tool_calls(response)
        
        if tool_calls:
            for tool_call in tool_calls:
                # Validate JSON format
                if not self._is_valid_json(tool_call):
                    warnings.append("Tool call contains invalid JSON format")
                    continue
                
                try:
                    tool_data = json.loads(tool_call)
                    tool_name = tool_data.get('tool_name') or tool_data.get('function')
                    
                    # Check if tool is in expected tools
                    if expected_tools and tool_name not in expected_tools:
                        warnings.append(f"Unexpected tool usage: {tool_name}")
                    
                    # Validate tool parameters
                    if not tool_data.get('parameters'):
                        warnings.append(f"Tool call missing parameters: {tool_name}")
                
                except json.JSONDecodeError:
                    warnings.append("Failed to parse tool call JSON")
        
        return len(warnings) == 0, warnings
    
    def _assess_response_quality(self, response: str, original_prompt: str) -> float:
        """Assess the overall quality of the response."""
        quality_score = 1.0
        
        # Check for completeness
        if self._appears_incomplete(response):
            quality_score *= 0.8
        
        # Check for clarity
        if self._lacks_clarity(response):
            quality_score *= 0.9
        
        # Check for specificity
        if self._is_too_vague(response):
            quality_score *= 0.85
        
        # Check for helpfulness
        if self._is_unhelpful_response(response):
            quality_score *= 0.7
        
        # Bonus for well-structured responses
        if self._is_well_structured(response):
            quality_score *= 1.1
        
        return min(quality_score, 1.0)  # Cap at 1.0
    
    def _validate_language_and_tone(self, response: str, user_context: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Validate language and tone appropriateness."""
        warnings = []
        
        # Check language consistency
        user_language = user_context.get('user_info', {}).get('language_code', 'en')
        if user_language not in self.config['allowed_languages']:
            warnings.append(f"User language ({user_language}) not in allowed languages")
        
        # Check tone appropriateness
        if self._has_inappropriate_tone(response):
            warnings.append("Response tone may be inappropriate")
        
        # Check for professional language
        if self._lacks_professionalism(response):
            warnings.append("Response may lack professional tone")
        
        return len(warnings) == 0, warnings
    
    def _extract_tool_calls(self, response: str) -> List[str]:
        """Extract tool calls from the response."""
        matches = self._tool_call_pattern.findall(response)
        return matches
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract keywords from text for relevance checking."""
        # Simple keyword extraction - in production, use more sophisticated NLP
        words = re.findall(r'\b\w{3,}\b', text.lower())
        # Filter out common words
        stop_words = {'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'she', 'use', 'way', 'will'}
        return [word for word in words if word not in stop_words][:10]  # Top 10 keywords
    
    def _is_valid_json(self, text: str) -> bool:
        """Check if text is valid JSON."""
        try:
            json.loads(text)
            return True
        except json.JSONDecodeError:
            return False
    
    def _is_unhelpful_response(self, response: str) -> bool:
        """Check if response is unhelpful."""
        unhelpful_phrases = [
            "i don't know",
            "i can't help",
            "sorry, i cannot",
            "i'm not sure",
            "i don't understand"
        ]
        response_lower = response.lower()
        return any(phrase in response_lower for phrase in unhelpful_phrases)
    
    def _contains_inappropriate_language(self, response: str) -> bool:
        """Check for inappropriate language."""
        # Basic implementation - in production, use comprehensive content moderation
        inappropriate_words = ['spam', 'scam']  # Add more as needed
        response_lower = response.lower()
        return any(word in response_lower for word in inappropriate_words)
    
    def _is_generic_response(self, response: str) -> bool:
        """Check if response is too generic."""
        generic_phrases = [
            "thank you for your question",
            "i hope this helps",
            "please let me know if you need anything else",
            "is there anything else i can help you with"
        ]
        response_lower = response.lower()
        generic_count = sum(1 for phrase in generic_phrases if phrase in response_lower)
        return generic_count >= 2  # Too many generic phrases
    
    def _appears_incomplete(self, response: str) -> bool:
        """Check if response appears incomplete."""
        # Check for abrupt endings
        if response.endswith(('...', 'and', 'but', 'however')):
            return True
        
        # Check for very short responses to complex prompts
        if len(response.split()) < 10:
            return True
        
        return False
    
    def _lacks_clarity(self, response: str) -> bool:
        """Check if response lacks clarity."""
        # Check for excessive use of vague terms
        vague_terms = ['maybe', 'perhaps', 'possibly', 'might', 'could be', 'seems like']
        vague_count = sum(response.lower().count(term) for term in vague_terms)
        return vague_count > 3
    
    def _is_too_vague(self, response: str) -> bool:
        """Check if response is too vague."""
        # Check for lack of specific information
        specific_indicators = [':', 'step', 'first', 'second', 'example', 'specifically']
        has_specifics = any(indicator in response.lower() for indicator in specific_indicators)
        return not has_specifics and len(response.split()) > 20
    
    def _is_well_structured(self, response: str) -> bool:
        """Check if response is well-structured."""
        # Check for structure indicators
        structure_indicators = ['\n', '1.', '2.', '-', '*', ':']
        return any(indicator in response for indicator in structure_indicators)
    
    def _has_inappropriate_tone(self, response: str) -> bool:
        """Check for inappropriate tone."""
        # Check for overly casual or inappropriate tone
        inappropriate_tone_indicators = ['lol', 'omg', 'wtf', 'whatever']
        response_lower = response.lower()
        return any(indicator in response_lower for indicator in inappropriate_tone_indicators)
    
    def _lacks_professionalism(self, response: str) -> bool:
        """Check if response lacks professionalism."""
        # Check for unprofessional language patterns
        unprofessional_patterns = [
            r'\b(gonna|wanna|gotta)\b',
            r'\b(yeah|yep|nope)\b',
            r'!!+'  # Multiple exclamation marks
        ]
        
        for pattern in unprofessional_patterns:
            if re.search(pattern, response, re.IGNORECASE):
                return True
        
        return False
    
    def update_config(self, new_config: Dict[str, Any]) -> None:
        """Update validator configuration."""
        self.config.update(new_config)
        logger.info(f"Updated response validator configuration: {new_config}")
    
    def get_validation_stats(self) -> Dict[str, Any]:
        """Get validation statistics."""
        return {
            'config': self.config,
            'patterns_loaded': len(self._harmful_content_patterns),
            'last_updated': datetime.now().isoformat()
        }
