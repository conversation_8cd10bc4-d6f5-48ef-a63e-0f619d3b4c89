"""
Main postprocessor orchestrator for the AI agent.

This module coordinates all postprocessing components to handle LLM responses,
execute tools, validate outputs, and prepare final responses for users.
It orchestrates the entire postprocessing pipeline and publishes events.

Pipeline stages:
1. LLM response validation and parsing
2. Tool extraction and execution
3. Response evaluation and quality assessment
4. Final response preparation and formatting
5. Event publishing for bot response delivery

The postprocessor ensures that all postprocessing steps are executed in the
correct order and handles errors gracefully with appropriate fallbacks.
"""

import json
import logging
import re
from typing import Dict, Any, List, Optional
from datetime import datetime

from .response_validator import ResponseValidator, ResponseValidationResult
from .tool_executor import ToolExecutor, ToolExecutionResult
from .response_evaluator import ResponseEvaluator, ResponseEvaluation
from ..utils.events import (
    LLMResponseReceived, ToolExecutionStarted, ToolExecutionCompleted,
    ResponseReady, ErrorOccurred
)
from ..utils.event_bus import event_bus

logger = logging.getLogger(__name__)


class Postprocessor:
    """
    Main postprocessor that orchestrates the entire postprocessing pipeline.
    
    Coordinates response validation, tool execution, response evaluation,
    and final response preparation for delivery to users.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the postprocessor with all required components.
        
        Args:
            config: Configuration dictionary for postprocessing components
        """
        # Initialize all postprocessing components
        self.response_validator = ResponseValidator(config.get('validator', {}) if config else {})
        self.tool_executor = ToolExecutor(config.get('tool_executor', {}) if config else {})
        self.response_evaluator = ResponseEvaluator(config.get('evaluator', {}) if config else {})
        
        # Configuration
        self.config = {
            'enable_tool_execution': True,
            'enable_response_evaluation': True,
            'require_validation_pass': True,
            'fallback_on_tool_failure': True,
            'max_tool_execution_time': 60,
            'enable_response_enhancement': True
        }
        
        if config:
            self.config.update(config.get('postprocessor', {}))
        
        # Subscribe to LLM response events using the new event bus
        event_bus.subscribe_tagged_methods(self)
        
        logger.info("Postprocessor initialized and subscribed to LLMResponseReceived events")
    
    @event_bus.subscribe("LLMResponseReceived")
    async def process_llm_response(self, event: LLMResponseReceived) -> None:
        """
        Process an LLM response through the complete postprocessing pipeline.
        
        Args:
            event: LLMResponseReceived event containing the response to process
        """
        try:
            logger.info(f"Starting postprocessing for LLM response from user {event.user_id}")
            
            # Stage 1: Response validation
            validation_result = await self._validate_response(event)
            if self.config['require_validation_pass'] and not validation_result.is_valid:
                await self._handle_validation_failure(event, validation_result)
                return
            
            # Stage 2: Tool execution
            tool_execution_results = []
            if self.config['enable_tool_execution'] and event.tools_to_execute:
                tool_execution_results = await self._execute_tools(event, validation_result)
            
            # Stage 3: Response evaluation
            evaluation_result = None
            if self.config['enable_response_evaluation']:
                evaluation_result = await self._evaluate_response(event, validation_result, tool_execution_results)
            
            # Stage 4: Final response preparation
            final_response = await self._prepare_final_response(
                event, validation_result, tool_execution_results, evaluation_result
            )
            
            # Stage 5: Publish response ready event
            await self._publish_response_ready_event(event, final_response, tool_execution_results, evaluation_result)
            
            logger.info(f"Successfully completed postprocessing for user {event.user_id}")
            
        except Exception as e:
            logger.error(f"Unexpected error in postprocessing pipeline: {e}", exc_info=True)
            await self._handle_unexpected_error(event, e)
    
    async def _validate_response(self, event: LLMResponseReceived) -> ResponseValidationResult:
        """
        Validate the LLM response.
        
        Args:
            event: LLMResponseReceived event
            
        Returns:
            ResponseValidationResult with validation status and details
        """
        try:
            logger.debug(f"Validating LLM response for user {event.user_id}")
            
            # Extract original prompt from response metadata if available
            original_prompt = event.response_metadata.get('original_prompt', '')
            
            # Create user context from event
            user_context = {
                'user_info': {
                    'user_id': event.user_id,
                    'chat_id': event.chat_id
                }
            }
            
            # Extract expected tools from event
            expected_tools = [tool.get('name') for tool in event.tools_to_execute if tool.get('name')]
            
            validation_result = await self.response_validator.validate_response(
                llm_response=event.llm_response,
                original_prompt=original_prompt,
                user_context=user_context,
                expected_tools=expected_tools
            )
            
            if validation_result.warnings:
                logger.warning(f"Response validation warnings for user {event.user_id}: {validation_result.warnings}")
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Error during response validation: {e}", exc_info=True)
            # Return failed validation result
            return ResponseValidationResult(
                is_valid=False,
                confidence_score=0.0,
                validation_errors=[f"Validation error: {str(e)}"]
            )
    
    async def _execute_tools(self, event: LLMResponseReceived, validation_result: ResponseValidationResult) -> List[ToolExecutionResult]:
        """
        Execute tools requested in the LLM response.
        
        Args:
            event: LLMResponseReceived event
            validation_result: Response validation results
            
        Returns:
            List of tool execution results
        """
        try:
            logger.debug(f"Executing {len(event.tools_to_execute)} tools for user {event.user_id}")
            
            # Create user context for tool execution
            user_context = {
                'user_info': {
                    'user_id': event.user_id,
                    'chat_id': event.chat_id
                },
                'response_metadata': event.response_metadata
            }
            
            # Publish tool execution started events
            for tool_call in event.tools_to_execute:
                await event_bus.publish(ToolExecutionStarted(
                    user_id=event.user_id,
                    chat_id=event.chat_id,
                    tool_name=tool_call.get('name', 'unknown'),
                    tool_parameters=tool_call.get('parameters', {}),
                    execution_id=f"{event.user_id}_{datetime.now().timestamp()}"
                ))
            
            # Execute tools
            execution_results = await self.tool_executor.execute_tools(
                tool_calls=event.tools_to_execute,
                user_context=user_context
            )
            
            # Publish tool execution completed events
            for i, result in enumerate(execution_results):
                tool_call = event.tools_to_execute[i] if i < len(event.tools_to_execute) else {}
                
                await event_bus.publish(ToolExecutionCompleted(
                    user_id=event.user_id,
                    chat_id=event.chat_id,
                    tool_name=tool_call.get('name', 'unknown'),
                    execution_id=result.metadata.get('execution_id', 'unknown') if result.metadata else 'unknown',
                    success=result.success,
                    result=result.result,
                    error_message=result.error_message,
                    execution_time_ms=result.execution_time_ms
                ))
            
            successful_executions = sum(1 for result in execution_results if result.success)
            logger.info(f"Tool execution completed: {successful_executions}/{len(execution_results)} successful")
            
            return execution_results
            
        except Exception as e:
            logger.error(f"Error during tool execution: {e}", exc_info=True)
            # Return error results for all tools
            return [
                ToolExecutionResult(
                    success=False,
                    error_message=f"Tool execution error: {str(e)}",
                    metadata={'tool_call': tool_call}
                )
                for tool_call in event.tools_to_execute
            ]
    
    async def _evaluate_response(self, 
                               event: LLMResponseReceived,
                               validation_result: ResponseValidationResult,
                               tool_execution_results: List[ToolExecutionResult]) -> Optional[ResponseEvaluation]:
        """
        Evaluate the overall response quality.
        
        Args:
            event: LLMResponseReceived event
            validation_result: Response validation results
            tool_execution_results: Tool execution results
            
        Returns:
            ResponseEvaluation with quality metrics and recommendations
        """
        try:
            logger.debug(f"Evaluating response quality for user {event.user_id}")
            
            # Extract original prompt from response metadata
            original_prompt = event.response_metadata.get('original_prompt', '')
            
            # Create user context for evaluation
            user_context = {
                'user_info': {
                    'user_id': event.user_id,
                    'chat_id': event.chat_id
                }
            }
            
            # Convert tool execution results to evaluation format
            tool_results_for_eval = [
                {
                    'success': result.success,
                    'result': result.result,
                    'tool_name': result.metadata.get('tool_name') if result.metadata else 'unknown',
                    'execution_time_ms': result.execution_time_ms
                }
                for result in tool_execution_results
            ]
            
            # Convert validation result to evaluation format
            validation_for_eval = {
                'is_valid': validation_result.is_valid,
                'confidence_score': validation_result.confidence_score,
                'validation_errors': validation_result.validation_errors,
                'warnings': validation_result.warnings
            }
            
            evaluation_result = await self.response_evaluator.evaluate_response(
                llm_response=event.llm_response,
                original_prompt=original_prompt,
                user_context=user_context,
                tool_execution_results=tool_results_for_eval,
                validation_result=validation_for_eval
            )
            
            logger.info(f"Response evaluation completed. Overall score: {evaluation_result.overall_score:.3f}")
            return evaluation_result
            
        except Exception as e:
            logger.error(f"Error during response evaluation: {e}", exc_info=True)
            return None
    
    async def _prepare_final_response(self,
                                    event: LLMResponseReceived,
                                    validation_result: ResponseValidationResult,
                                    tool_execution_results: List[ToolExecutionResult],
                                    evaluation_result: Optional[ResponseEvaluation]) -> str:
        """
        Prepare the final response for delivery to the user.
        
        Args:
            event: LLMResponseReceived event
            validation_result: Response validation results
            tool_execution_results: Tool execution results
            evaluation_result: Response evaluation results
            
        Returns:
            Final response text ready for delivery
        """
        try:
            logger.debug(f"Preparing final response for user {event.user_id}")
            
            # Start with the original LLM response
            final_response = event.llm_response
            
            # Enhance response with tool results if enabled
            if self.config['enable_response_enhancement'] and tool_execution_results:
                final_response = self._enhance_response_with_tool_results(final_response, tool_execution_results)
            
            # Apply any necessary corrections based on validation
            if validation_result.suggested_corrections:
                final_response = self._apply_validation_corrections(final_response, validation_result)
            
            # Add quality improvements based on evaluation
            if evaluation_result and evaluation_result.recommendations:
                final_response = self._apply_evaluation_improvements(final_response, evaluation_result)
            
            # Ensure response is not empty
            if not final_response.strip():
                final_response = "I apologize, but I'm having trouble generating a proper response. Please try again."
            
            logger.debug(f"Final response prepared with {len(final_response)} characters")
            return final_response
            
        except Exception as e:
            logger.error(f"Error preparing final response: {e}", exc_info=True)
            return "I apologize, but I encountered an error while preparing my response. Please try again."
    
    def _enhance_response_with_tool_results(self, response: str, tool_results: List[ToolExecutionResult]) -> str:
        """Enhance response with tool execution results."""
        enhanced_response = response
        
        # Add successful tool results to response
        for result in tool_results:
            if result.success and result.result:
                tool_name = result.metadata.get('tool_name', 'tool') if result.metadata else 'tool'
                
                # Check if tool result is already integrated
                if str(result.result).lower() not in response.lower():
                    # Add tool result information
                    if isinstance(result.result, dict):
                        if result.result.get('message'):
                            enhanced_response += f"\n\n✅ {result.result['message']}"
                        elif result.result.get('confirmation'):
                            enhanced_response += f"\n\n✅ {result.result['confirmation']}"
                    else:
                        enhanced_response += f"\n\n✅ {tool_name} completed successfully."
        
        # Add information about failed tools
        failed_tools = [result for result in tool_results if not result.success]
        if failed_tools:
            enhanced_response += f"\n\n⚠️ Note: Some operations could not be completed due to technical issues."
        
        return enhanced_response
    
    def _apply_validation_corrections(self, response: str, validation_result: ResponseValidationResult) -> str:
        """Apply corrections based on validation results."""
        corrected_response = response
        
        if validation_result.suggested_corrections:
            # Apply basic corrections (this is a simplified implementation)
            for correction in validation_result.suggested_corrections:
                if "length" in correction.lower():
                    # If response is too long, add a summary note
                    if len(response) > 2000:
                        corrected_response = response[:1800] + "\n\n[Response truncated for brevity]"
        
        return corrected_response
    
    def _apply_evaluation_improvements(self, response: str, evaluation_result: ResponseEvaluation) -> str:
        """Apply improvements based on evaluation results."""
        improved_response = response
        
        # If response quality is low, add a disclaimer
        if evaluation_result.overall_score < 0.7:
            improved_response += "\n\n💡 If this response doesn't fully address your needs, please let me know and I'll be happy to help further."
        
        return improved_response
    
    async def _publish_response_ready_event(self,
                                          original_event: LLMResponseReceived,
                                          final_response: str,
                                          tool_execution_results: List[ToolExecutionResult],
                                          evaluation_result: Optional[ResponseEvaluation]) -> None:
        """
        Publish the ResponseReady event.
        
        Args:
            original_event: Original LLMResponseReceived event
            final_response: Final prepared response
            tool_execution_results: Tool execution results
            evaluation_result: Response evaluation results
        """
        try:
            # Prepare metadata
            metadata = {
                'original_llm_response_length': len(original_event.llm_response),
                'final_response_length': len(final_response),
                'tools_executed': len(tool_execution_results),
                'successful_tools': sum(1 for result in tool_execution_results if result.success),
                'processing_completed_at': datetime.now().isoformat()
            }
            
            if evaluation_result:
                metadata.update({
                    'quality_score': evaluation_result.overall_score,
                    'quality_level': evaluation_result.evaluation_details.get('quality_level'),
                    'recommendations_count': len(evaluation_result.recommendations)
                })
            
            # Prepare attachments (tool results, if any)
            attachments = []
            for result in tool_execution_results:
                if result.success and result.result:
                    attachments.append({
                        'type': 'tool_result',
                        'tool_name': result.metadata.get('tool_name') if result.metadata else 'unknown',
                        'data': result.result
                    })
            
            # Publish the response ready event
            response_ready_event = ResponseReady(
                user_id=original_event.user_id,
                chat_id=original_event.chat_id,
                response_text=final_response,
                response_type='text',
                attachments=attachments if attachments else None,
                metadata=metadata
            )
            
            await event_bus.publish(response_ready_event)
            logger.info(f"Published ResponseReady event for user {original_event.user_id}")
            
        except Exception as e:
            logger.error(f"Error publishing response ready event: {e}", exc_info=True)
            await self._handle_unexpected_error(original_event, e)
    
    async def _handle_validation_failure(self, event: LLMResponseReceived, validation_result: ResponseValidationResult) -> None:
        """Handle response validation failures."""
        error_message = f"Response validation failed: {', '.join(validation_result.validation_errors or [])}"
        
        await event_bus.publish(ErrorOccurred(
            user_id=event.user_id,
            chat_id=event.chat_id,
            error_type="response_validation_error",
            error_message=error_message,
            component="postprocessor.response_validator",
            context={'validation_result': validation_result.__dict__},
            timestamp=datetime.now()
        ))
        
        # Send a fallback response
        fallback_response = "I apologize, but I'm having trouble generating a proper response. Please try rephrasing your request."
        
        await event_bus.publish(ResponseReady(
            user_id=event.user_id,
            chat_id=event.chat_id,
            response_text=fallback_response,
            response_type='text',
            metadata={'fallback_response': True, 'validation_failed': True}
        ))
        
        logger.warning(f"Response validation failed for user {event.user_id}: {error_message}")
    
    async def _handle_unexpected_error(self, event: LLMResponseReceived, error: Exception) -> None:
        """Handle unexpected errors in the postprocessing pipeline."""
        await event_bus.publish(ErrorOccurred(
            user_id=event.user_id,
            chat_id=event.chat_id,
            error_type="unexpected_error",
            error_message=str(error),
            component="postprocessor",
            stack_trace=str(error.__traceback__) if error.__traceback__ else None,
            timestamp=datetime.now()
        ))
        
        # Send a fallback response
        fallback_response = "I apologize, but I encountered an unexpected error. Please try again."
        
        await event_bus.publish(ResponseReady(
            user_id=event.user_id,
            chat_id=event.chat_id,
            response_text=fallback_response,
            response_type='text',
            metadata={'fallback_response': True, 'unexpected_error': True}
        ))
        
        logger.error(f"Unexpected error in postprocessing for user {event.user_id}: {error}")
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get health status of the postprocessor and its components."""
        return {
            'status': 'healthy',
            'components': {
                'response_validator': 'healthy',
                'tool_executor': 'healthy',
                'response_evaluator': 'healthy'
            },
            'config': self.config,
            'last_check': datetime.now().isoformat()
        }
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get postprocessing statistics."""
        tool_stats = self.tool_executor.get_execution_stats()
        evaluation_trends = self.response_evaluator.get_evaluation_trends()
        
        return {
            'tool_execution': tool_stats,
            'response_evaluation': evaluation_trends,
            'config': self.config,
            'last_updated': datetime.now().isoformat()
        }
