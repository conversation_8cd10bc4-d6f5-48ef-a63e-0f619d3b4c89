"""
Response evaluation component for postprocessing pipeline.

This module evaluates the overall quality and effectiveness of LLM responses
after validation and tool execution. It provides metrics and feedback for
response improvement and system optimization.

Evaluation criteria include:
- Response completeness and accuracy
- User satisfaction prediction
- Tool usage effectiveness
- Response coherence and flow
- Personalization quality
- Error handling appropriateness

The evaluator helps improve the AI agent's performance over time by
providing detailed feedback and quality metrics.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import re

logger = logging.getLogger(__name__)


@dataclass
class ResponseEvaluation:
    """Comprehensive evaluation of an LLM response."""
    overall_score: float
    completeness_score: float
    accuracy_score: float
    helpfulness_score: float
    personalization_score: float
    tool_usage_score: float
    coherence_score: float
    evaluation_details: Dict[str, Any]
    recommendations: List[str]
    metadata: Dict[str, Any]


class ResponseEvaluator:
    """
    Evaluates LLM responses for quality, effectiveness, and user satisfaction.
    
    Provides comprehensive analysis of response quality across multiple
    dimensions to enable continuous improvement of the AI agent.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the response evaluator.
        
        Args:
            config: Configuration dictionary for evaluation criteria
        """
        # Default configuration
        self.config = {
            'weights': {
                'completeness': 0.25,
                'accuracy': 0.25,
                'helpfulness': 0.20,
                'personalization': 0.15,
                'tool_usage': 0.10,
                'coherence': 0.05
            },
            'minimum_acceptable_score': 0.7,
            'enable_detailed_analysis': True,
            'track_improvement_metrics': True,
            'personalization_factors': ['user_name', 'preferences', 'context_awareness'],
            'quality_thresholds': {
                'excellent': 0.9,
                'good': 0.8,
                'acceptable': 0.7,
                'poor': 0.5
            }
        }
        
        if config:
            self.config.update(config)
        
        # Evaluation history for trend analysis
        self._evaluation_history: List[Dict[str, Any]] = []
        
        logger.info("Response evaluator initialized")
    
    async def evaluate_response(self,
                              llm_response: str,
                              original_prompt: str,
                              user_context: Dict[str, Any],
                              tool_execution_results: List[Dict[str, Any]] = None,
                              validation_result: Dict[str, Any] = None) -> ResponseEvaluation:
        """
        Comprehensively evaluate an LLM response.
        
        Args:
            llm_response: The LLM's response to evaluate
            original_prompt: The original prompt sent to the LLM
            user_context: User context and conversation information
            tool_execution_results: Results from any tool executions
            validation_result: Results from response validation
            
        Returns:
            ResponseEvaluation with detailed scores and recommendations
        """
        try:
            logger.debug("Starting comprehensive response evaluation")
            
            # Individual dimension scores
            completeness_score = self._evaluate_completeness(llm_response, original_prompt)
            accuracy_score = self._evaluate_accuracy(llm_response, user_context, tool_execution_results)
            helpfulness_score = self._evaluate_helpfulness(llm_response, original_prompt)
            personalization_score = self._evaluate_personalization(llm_response, user_context)
            tool_usage_score = self._evaluate_tool_usage(llm_response, tool_execution_results)
            coherence_score = self._evaluate_coherence(llm_response)
            
            # Calculate weighted overall score
            weights = self.config['weights']
            overall_score = (
                completeness_score * weights['completeness'] +
                accuracy_score * weights['accuracy'] +
                helpfulness_score * weights['helpfulness'] +
                personalization_score * weights['personalization'] +
                tool_usage_score * weights['tool_usage'] +
                coherence_score * weights['coherence']
            )
            
            # Generate evaluation details
            evaluation_details = {
                'scores': {
                    'completeness': completeness_score,
                    'accuracy': accuracy_score,
                    'helpfulness': helpfulness_score,
                    'personalization': personalization_score,
                    'tool_usage': tool_usage_score,
                    'coherence': coherence_score
                },
                'quality_level': self._determine_quality_level(overall_score),
                'response_length': len(llm_response),
                'response_word_count': len(llm_response.split()),
                'validation_passed': validation_result.get('is_valid', True) if validation_result else True,
                'tools_executed': len(tool_execution_results) if tool_execution_results else 0
            }
            
            # Generate recommendations
            recommendations = self._generate_recommendations(
                overall_score, evaluation_details, llm_response, user_context
            )
            
            # Create evaluation result
            evaluation = ResponseEvaluation(
                overall_score=overall_score,
                completeness_score=completeness_score,
                accuracy_score=accuracy_score,
                helpfulness_score=helpfulness_score,
                personalization_score=personalization_score,
                tool_usage_score=tool_usage_score,
                coherence_score=coherence_score,
                evaluation_details=evaluation_details,
                recommendations=recommendations,
                metadata={
                    'evaluation_timestamp': datetime.now().isoformat(),
                    'evaluator_version': '1.0',
                    'config_used': self.config.copy()
                }
            )
            
            # Store evaluation for trend analysis
            if self.config['track_improvement_metrics']:
                self._store_evaluation_history(evaluation, user_context)
            
            logger.info(f"Response evaluation completed. Overall score: {overall_score:.3f}")
            return evaluation
            
        except Exception as e:
            logger.error(f"Error during response evaluation: {e}", exc_info=True)
            # Return a default evaluation with error indication
            return ResponseEvaluation(
                overall_score=0.0,
                completeness_score=0.0,
                accuracy_score=0.0,
                helpfulness_score=0.0,
                personalization_score=0.0,
                tool_usage_score=0.0,
                coherence_score=0.0,
                evaluation_details={'error': str(e)},
                recommendations=['Evaluation failed - manual review required'],
                metadata={'error_occurred': True}
            )
    
    def _evaluate_completeness(self, response: str, original_prompt: str) -> float:
        """Evaluate how completely the response addresses the original prompt."""
        score = 1.0
        
        # Check if response addresses the main request
        if not self._addresses_main_request(response, original_prompt):
            score *= 0.6
        
        # Check for incomplete responses
        if self._appears_incomplete(response):
            score *= 0.7
        
        # Check for comprehensive coverage
        if self._provides_comprehensive_coverage(response, original_prompt):
            score *= 1.1
        
        # Bonus for providing additional helpful information
        if self._provides_additional_value(response):
            score *= 1.05
        
        return min(score, 1.0)
    
    def _evaluate_accuracy(self, response: str, user_context: Dict[str, Any], tool_results: List[Dict[str, Any]]) -> float:
        """Evaluate the accuracy of the response."""
        score = 1.0
        
        # Check for factual consistency
        if not self._is_factually_consistent(response):
            score *= 0.8
        
        # Check tool result integration
        if tool_results:
            if not self._integrates_tool_results_correctly(response, tool_results):
                score *= 0.7
        
        # Check for contradictions
        if self._contains_contradictions(response):
            score *= 0.6
        
        # Check temporal accuracy (dates, times)
        if not self._has_temporal_accuracy(response, user_context):
            score *= 0.9
        
        return score
    
    def _evaluate_helpfulness(self, response: str, original_prompt: str) -> float:
        """Evaluate how helpful the response is to the user."""
        score = 1.0
        
        # Check for actionable information
        if self._provides_actionable_information(response):
            score *= 1.1
        
        # Check for clear instructions
        if self._provides_clear_instructions(response):
            score *= 1.05
        
        # Check for unhelpful responses
        if self._is_unhelpful_response(response):
            score *= 0.5
        
        # Check for proactive assistance
        if self._offers_proactive_assistance(response):
            score *= 1.1
        
        return min(score, 1.0)
    
    def _evaluate_personalization(self, response: str, user_context: Dict[str, Any]) -> float:
        """Evaluate how well the response is personalized to the user."""
        score = 0.5  # Base score for generic responses
        
        user_info = user_context.get('user_info', {})
        
        # Check for name usage
        if user_info.get('first_name') and user_info['first_name'].lower() in response.lower():
            score += 0.2
        
        # Check for preference consideration
        if self._considers_user_preferences(response, user_context):
            score += 0.2
        
        # Check for context awareness
        if self._shows_context_awareness(response, user_context):
            score += 0.2
        
        # Check for appropriate language/tone
        if self._uses_appropriate_language(response, user_context):
            score += 0.1
        
        return min(score, 1.0)
    
    def _evaluate_tool_usage(self, response: str, tool_results: List[Dict[str, Any]]) -> float:
        """Evaluate the effectiveness of tool usage."""
        if not tool_results:
            # If no tools were expected or used, return neutral score
            return 0.8
        
        score = 1.0
        
        # Check if tools were used appropriately
        successful_tools = sum(1 for result in tool_results if result.get('success', False))
        total_tools = len(tool_results)
        
        if total_tools > 0:
            success_rate = successful_tools / total_tools
            score *= success_rate
        
        # Check if tool results are properly integrated
        if not self._integrates_tool_results_correctly(response, tool_results):
            score *= 0.8
        
        # Check for unnecessary tool usage
        if self._has_unnecessary_tool_usage(response, tool_results):
            score *= 0.9
        
        return score
    
    def _evaluate_coherence(self, response: str) -> float:
        """Evaluate the coherence and flow of the response."""
        score = 1.0
        
        # Check for logical flow
        if not self._has_logical_flow(response):
            score *= 0.8
        
        # Check for clear structure
        if self._has_clear_structure(response):
            score *= 1.05
        
        # Check for repetition
        if self._has_excessive_repetition(response):
            score *= 0.9
        
        # Check for abrupt transitions
        if self._has_abrupt_transitions(response):
            score *= 0.9
        
        return min(score, 1.0)
    
    def _determine_quality_level(self, overall_score: float) -> str:
        """Determine quality level based on overall score."""
        thresholds = self.config['quality_thresholds']
        
        if overall_score >= thresholds['excellent']:
            return 'excellent'
        elif overall_score >= thresholds['good']:
            return 'good'
        elif overall_score >= thresholds['acceptable']:
            return 'acceptable'
        elif overall_score >= thresholds['poor']:
            return 'poor'
        else:
            return 'unacceptable'
    
    def _generate_recommendations(self, 
                                overall_score: float, 
                                evaluation_details: Dict[str, Any], 
                                response: str, 
                                user_context: Dict[str, Any]) -> List[str]:
        """Generate recommendations for improvement."""
        recommendations = []
        
        scores = evaluation_details['scores']
        
        # Completeness recommendations
        if scores['completeness'] < 0.7:
            recommendations.append("Improve response completeness by addressing all aspects of the user's request")
        
        # Accuracy recommendations
        if scores['accuracy'] < 0.8:
            recommendations.append("Verify factual accuracy and ensure tool results are correctly integrated")
        
        # Helpfulness recommendations
        if scores['helpfulness'] < 0.7:
            recommendations.append("Provide more actionable information and clear next steps")
        
        # Personalization recommendations
        if scores['personalization'] < 0.6:
            recommendations.append("Increase personalization by using user's name and considering their preferences")
        
        # Tool usage recommendations
        if scores['tool_usage'] < 0.7:
            recommendations.append("Improve tool selection and integration of tool results")
        
        # Coherence recommendations
        if scores['coherence'] < 0.8:
            recommendations.append("Improve response structure and logical flow")
        
        # Overall quality recommendations
        if overall_score < self.config['minimum_acceptable_score']:
            recommendations.append("Overall response quality is below acceptable threshold - consider regeneration")
        
        return recommendations
    
    def _store_evaluation_history(self, evaluation: ResponseEvaluation, user_context: Dict[str, Any]) -> None:
        """Store evaluation in history for trend analysis."""
        history_entry = {
            'timestamp': datetime.now().isoformat(),
            'overall_score': evaluation.overall_score,
            'scores': evaluation.evaluation_details['scores'],
            'quality_level': evaluation.evaluation_details['quality_level'],
            'user_id': user_context.get('user_info', {}).get('user_id'),
            'recommendations_count': len(evaluation.recommendations)
        }
        
        self._evaluation_history.append(history_entry)
        
        # Keep only last 1000 evaluations
        if len(self._evaluation_history) > 1000:
            self._evaluation_history = self._evaluation_history[-1000:]
    
    # Helper methods for specific evaluation criteria
    def _addresses_main_request(self, response: str, prompt: str) -> bool:
        """Check if response addresses the main request in the prompt."""
        # Extract key action words from prompt
        action_words = re.findall(r'\b(create|schedule|set|find|search|help|show|tell|explain)\b', prompt.lower())
        
        # Check if response contains relevant action indicators
        response_lower = response.lower()
        return any(word in response_lower for word in action_words) if action_words else True
    
    def _appears_incomplete(self, response: str) -> bool:
        """Check if response appears incomplete."""
        incomplete_indicators = ['...', 'and so on', 'etc.', 'to be continued']
        return any(indicator in response.lower() for indicator in incomplete_indicators)
    
    def _provides_comprehensive_coverage(self, response: str, prompt: str) -> bool:
        """Check if response provides comprehensive coverage of the topic."""
        # Simple heuristic: longer responses with structure tend to be more comprehensive
        return len(response.split()) > 50 and ('1.' in response or '-' in response or ':' in response)
    
    def _provides_additional_value(self, response: str) -> bool:
        """Check if response provides additional helpful information."""
        value_indicators = ['tip:', 'note:', 'also', 'additionally', 'furthermore', 'by the way']
        return any(indicator in response.lower() for indicator in value_indicators)
    
    def _is_factually_consistent(self, response: str) -> bool:
        """Check for basic factual consistency (simplified implementation)."""
        # Look for obvious contradictions or impossible statements
        contradiction_patterns = [
            r'both .* and .*',
            r'never .* always',
            r'impossible .* possible'
        ]
        
        for pattern in contradiction_patterns:
            if re.search(pattern, response.lower()):
                return False
        
        return True
    
    def _integrates_tool_results_correctly(self, response: str, tool_results: List[Dict[str, Any]]) -> bool:
        """Check if tool results are properly integrated into the response."""
        if not tool_results:
            return True
        
        # Check if response mentions tool results
        for result in tool_results:
            if result.get('success') and result.get('result'):
                # Look for integration of tool result data
                result_data = str(result['result'])
                if len(result_data) > 10 and result_data.lower() not in response.lower():
                    return False
        
        return True
    
    def _contains_contradictions(self, response: str) -> bool:
        """Check for internal contradictions in the response."""
        # Simple contradiction detection
        sentences = response.split('.')
        
        # Look for contradictory statements (simplified)
        positive_negative_pairs = [
            ('can', "can't"), ('will', "won't"), ('is', "isn't"),
            ('possible', 'impossible'), ('available', 'unavailable')
        ]
        
        for positive, negative in positive_negative_pairs:
            if positive in response.lower() and negative in response.lower():
                return True
        
        return False
    
    def _has_temporal_accuracy(self, response: str, user_context: Dict[str, Any]) -> bool:
        """Check for temporal accuracy in dates and times."""
        # Basic check for reasonable date/time references
        # In production, this would be more sophisticated
        
        # Check for obviously wrong dates
        wrong_date_patterns = [
            r'february 30',
            r'april 31',
            r'june 31',
            r'september 31',
            r'november 31'
        ]
        
        for pattern in wrong_date_patterns:
            if re.search(pattern, response.lower()):
                return False
        
        return True
    
    def _provides_actionable_information(self, response: str) -> bool:
        """Check if response provides actionable information."""
        action_indicators = [
            'you can', 'you should', 'try', 'click', 'go to', 'visit',
            'step 1', 'first', 'next', 'then', 'finally'
        ]
        
        return any(indicator in response.lower() for indicator in action_indicators)
    
    def _provides_clear_instructions(self, response: str) -> bool:
        """Check if response provides clear instructions."""
        instruction_indicators = ['1.', '2.', 'step', 'first', 'then', 'next', 'finally', '-']
        return any(indicator in response for indicator in instruction_indicators)
    
    def _is_unhelpful_response(self, response: str) -> bool:
        """Check if response is unhelpful."""
        unhelpful_phrases = [
            "i don't know", "i can't help", "sorry, i cannot",
            "i'm not sure", "i don't understand", "that's not possible"
        ]
        
        response_lower = response.lower()
        return any(phrase in response_lower for phrase in unhelpful_phrases)
    
    def _offers_proactive_assistance(self, response: str) -> bool:
        """Check if response offers proactive assistance."""
        proactive_indicators = [
            'would you like', 'i can also', 'additionally', 'you might also',
            'let me know if', 'feel free to', 'i can help you with'
        ]
        
        return any(indicator in response.lower() for indicator in proactive_indicators)
    
    def _considers_user_preferences(self, response: str, user_context: Dict[str, Any]) -> bool:
        """Check if response considers user preferences."""
        preferences = user_context.get('user_info', {}).get('preferences', {})
        
        if not preferences:
            return False
        
        # Check if any preference-related terms appear in response
        for pref_key, pref_value in preferences.items():
            if str(pref_value).lower() in response.lower():
                return True
        
        return False
    
    def _shows_context_awareness(self, response: str, user_context: Dict[str, Any]) -> bool:
        """Check if response shows awareness of conversation context."""
        # Check for references to previous conversation or user context
        context_indicators = [
            'as we discussed', 'earlier', 'previously', 'your', 'based on',
            'considering', 'given that', 'since you'
        ]
        
        return any(indicator in response.lower() for indicator in context_indicators)
    
    def _uses_appropriate_language(self, response: str, user_context: Dict[str, Any]) -> bool:
        """Check if response uses appropriate language for the user."""
        user_language = user_context.get('user_info', {}).get('language_code', 'en')
        
        # Basic check - in production, use language detection
        if user_language != 'en':
            # For non-English users, check if response acknowledges language preference
            return True  # Simplified for this implementation
        
        # Check for appropriate formality level
        informal_indicators = ['gonna', 'wanna', 'yeah', 'nope']
        is_too_informal = any(indicator in response.lower() for indicator in informal_indicators)
        
        return not is_too_informal
    
    def _has_unnecessary_tool_usage(self, response: str, tool_results: List[Dict[str, Any]]) -> bool:
        """Check for unnecessary tool usage."""
        # Simple heuristic: if tools were used but results aren't mentioned, might be unnecessary
        if not tool_results:
            return False
        
        for result in tool_results:
            if result.get('success'):
                tool_name = result.get('tool_name', '')
                if tool_name and tool_name.lower() not in response.lower():
                    return True
        
        return False
    
    def _has_logical_flow(self, response: str) -> bool:
        """Check if response has logical flow."""
        # Check for transition words and logical structure
        transition_words = ['however', 'therefore', 'additionally', 'furthermore', 'consequently', 'meanwhile']
        
        sentences = response.split('.')
        if len(sentences) > 3:
            # For longer responses, expect some transitions
            return any(word in response.lower() for word in transition_words)
        
        return True  # Short responses don't need complex transitions
    
    def _has_clear_structure(self, response: str) -> bool:
        """Check if response has clear structure."""
        structure_indicators = ['1.', '2.', '-', '*', ':', '\n']
        return any(indicator in response for indicator in structure_indicators)
    
    def _has_excessive_repetition(self, response: str) -> bool:
        """Check for excessive repetition."""
        words = response.lower().split()
        if len(words) < 10:
            return False
        
        # Check for repeated phrases
        word_counts = {}
        for word in words:
            if len(word) > 3:  # Only check meaningful words
                word_counts[word] = word_counts.get(word, 0) + 1
        
        # If any word appears more than 20% of the time, it's excessive
        max_count = max(word_counts.values()) if word_counts else 0
        return max_count > len(words) * 0.2
    
    def _has_abrupt_transitions(self, response: str) -> bool:
        """Check for abrupt transitions between topics."""
        # Simple heuristic: look for sudden topic changes without transitions
        sentences = response.split('.')
        
        if len(sentences) < 3:
            return False
        
        # This is a simplified implementation
        # In production, use more sophisticated NLP techniques
        return False
    
    def get_evaluation_trends(self) -> Dict[str, Any]:
        """Get evaluation trends and statistics."""
        if not self._evaluation_history:
            return {'message': 'No evaluation history available'}
        
        recent_evaluations = self._evaluation_history[-50:]  # Last 50 evaluations
        
        avg_score = sum(eval['overall_score'] for eval in recent_evaluations) / len(recent_evaluations)
        
        quality_distribution = {}
        for eval in recent_evaluations:
            quality = eval['quality_level']
            quality_distribution[quality] = quality_distribution.get(quality, 0) + 1
        
        return {
            'total_evaluations': len(self._evaluation_history),
            'recent_average_score': avg_score,
            'quality_distribution': quality_distribution,
            'improvement_trend': self._calculate_improvement_trend(),
            'last_evaluation': self._evaluation_history[-1] if self._evaluation_history else None
        }
    
    def _calculate_improvement_trend(self) -> str:
        """Calculate if scores are improving over time."""
        if len(self._evaluation_history) < 10:
            return 'insufficient_data'
        
        recent_scores = [eval['overall_score'] for eval in self._evaluation_history[-10:]]
        older_scores = [eval['overall_score'] for eval in self._evaluation_history[-20:-10]]
        
        if not older_scores:
            return 'insufficient_data'
        
        recent_avg = sum(recent_scores) / len(recent_scores)
        older_avg = sum(older_scores) / len(older_scores)
        
        if recent_avg > older_avg + 0.05:
            return 'improving'
        elif recent_avg < older_avg - 0.05:
            return 'declining'
        else:
            return 'stable'
    
    def update_config(self, new_config: Dict[str, Any]) -> None:
        """Update evaluator configuration."""
        self.config.update(new_config)
        logger.info(f"Updated response evaluator configuration: {new_config}")
    
    def clear_evaluation_history(self) -> None:
        """Clear evaluation history (useful for testing)."""
        self._evaluation_history.clear()
        logger.info("Cleared evaluation history")
