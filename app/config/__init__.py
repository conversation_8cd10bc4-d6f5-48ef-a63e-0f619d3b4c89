"""
Enhanced configuration module for the AI agent system.

This module provides comprehensive configuration management for all
components of the AI agent architecture. It supports environment-based
configuration, validation, and dynamic configuration updates.

Configuration categories:
- Database and storage configuration
- LLM provider configurations
- External API configurations (Calendar, Weather, etc.)
- Event bus and messaging configuration
- Logging and monitoring configuration
- Security and authentication configuration
- Performance and scaling configuration

The configuration system ensures that all components have access to
their required settings while maintaining security and flexibility.
"""
