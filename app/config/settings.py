"""
Enhanced settings configuration for the AI agent system.

This module provides comprehensive configuration management with support
for environment variables, validation, and component-specific settings
for the new event-driven architecture.

Features:
- Environment-based configuration loading
- Configuration validation and type checking
- Component-specific configuration sections
- Secret management and security
- Dynamic configuration updates
- Configuration inheritance and overrides

The settings system ensures all components have access to their
required configuration while maintaining security best practices.
"""

import os
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from pathlib import Path

from ..utils.constants import DEFAULT_CONFIG, TimeZone, Language, Platform
from ..utils.validation import validate_configuration

logger = logging.getLogger(__name__)


@dataclass
class DatabaseConfig:
    """Database configuration settings."""
    host: str = "localhost"
    port: int = 5432
    database: str = "zeitwahl"
    username: str = "zeitwahl_user"
    password: str = ""
    pool_size: int = 10
    max_overflow: int = 20
    echo: bool = False
    ssl_mode: str = "prefer"


@dataclass
class LLMProviderConfig:
    """Configuration for LLM providers."""
    api_key: str = ""
    model: str = ""
    base_url: str = ""
    timeout_seconds: int = 30
    max_tokens: int = 2000
    temperature: float = 0.7
    enabled: bool = True


@dataclass
class LLMConfig:
    """LLM service configuration."""
    providers: Dict[str, LLMProviderConfig] = field(default_factory=dict)
    provider_priority: List[str] = field(default_factory=lambda: ["openai", "anthropic", "local"])
    enable_failover: bool = True
    enable_caching: bool = False
    max_retries: int = 2
    timeout_seconds: int = 60


@dataclass
class CalendarProviderConfig:
    """Configuration for calendar providers."""
    api_key: str = ""
    calendar_id: str = "primary"
    enabled: bool = True
    sync_interval_minutes: int = 15


@dataclass
class CalendarConfig:
    """Calendar service configuration."""
    providers: Dict[str, CalendarProviderConfig] = field(default_factory=dict)
    provider_priority: List[str] = field(default_factory=lambda: ["google", "local"])
    enable_sync: bool = False
    default_timezone: str = TimeZone.UTC.value
    conflict_resolution: str = "manual"


@dataclass
class EventBusConfig:
    """Event bus configuration."""
    max_event_history: int = 1000
    event_processing_timeout: int = 30
    enable_event_logging: bool = True
    enable_middleware: bool = True
    max_concurrent_events: int = 100


@dataclass
class LoggingConfig:
    """Logging configuration."""
    level: str = "INFO"
    format: str = "structured"  # structured, simple, detailed
    log_directory: str = "logs"
    max_file_size_mb: int = 10
    backup_count: int = 5
    enable_console_logging: bool = True
    enable_file_logging: bool = True
    enable_error_file: bool = True


@dataclass
class SecurityConfig:
    """Security configuration."""
    secret_key: str = ""
    jwt_secret: str = ""
    jwt_expiration_hours: int = 24
    enable_rate_limiting: bool = True
    max_requests_per_minute: int = 60
    enable_cors: bool = True
    allowed_origins: List[str] = field(default_factory=list)


@dataclass
class TelegramConfig:
    """Telegram bot configuration."""
    token: str = ""
    webhook_url: str = ""
    webhook_secret: str = ""
    enable_webhook: bool = False
    polling_timeout: int = 30
    allowed_updates: List[str] = field(default_factory=lambda: ["message", "callback_query"])


@dataclass
class Settings:
    """Main settings configuration for the AI agent system."""
    
    # Environment
    environment: str = "development"
    debug: bool = False
    
    # Component configurations
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    llm: LLMConfig = field(default_factory=LLMConfig)
    calendar: CalendarConfig = field(default_factory=CalendarConfig)
    event_bus: EventBusConfig = field(default_factory=EventBusConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    telegram: TelegramConfig = field(default_factory=TelegramConfig)
    
    # Processing configurations
    preprocessing: Dict[str, Any] = field(default_factory=lambda: DEFAULT_CONFIG.copy())
    
    # Feature flags
    enable_analytics: bool = True
    enable_monitoring: bool = True
    enable_caching: bool = True
    enable_debug_mode: bool = False
    
    def __post_init__(self):
        """Post-initialization validation and setup."""
        self._load_from_environment()
        self._validate_configuration()
        self._setup_derived_settings()
    
    def _load_from_environment(self) -> None:
        """Load configuration from environment variables."""
        # Environment
        self.environment = os.getenv("ENVIRONMENT", self.environment)
        self.debug = os.getenv("DEBUG", "false").lower() == "true"
        
        # Database
        self.database.host = os.getenv("DB_HOST", self.database.host)
        self.database.port = int(os.getenv("DB_PORT", str(self.database.port)))
        self.database.database = os.getenv("DB_NAME", self.database.database)
        self.database.username = os.getenv("DB_USER", self.database.username)
        self.database.password = os.getenv("DB_PASSWORD", self.database.password)
        
        # Telegram
        self.telegram.token = os.getenv("TELEGRAM_BOT_TOKEN", self.telegram.token)
        self.telegram.webhook_url = os.getenv("TELEGRAM_WEBHOOK_URL", self.telegram.webhook_url)
        self.telegram.webhook_secret = os.getenv("TELEGRAM_WEBHOOK_SECRET", self.telegram.webhook_secret)
        
        # LLM Providers
        openai_config = LLMProviderConfig(
            api_key=os.getenv("OPENAI_API_KEY", ""),
            model=os.getenv("OPENAI_MODEL", "gpt-3.5-turbo"),
            base_url=os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1"),
            enabled=os.getenv("OPENAI_ENABLED", "true").lower() == "true"
        )
        
        anthropic_config = LLMProviderConfig(
            api_key=os.getenv("ANTHROPIC_API_KEY", ""),
            model=os.getenv("ANTHROPIC_MODEL", "claude-3-sonnet-20240229"),
            base_url=os.getenv("ANTHROPIC_BASE_URL", "https://api.anthropic.com/v1"),
            enabled=os.getenv("ANTHROPIC_ENABLED", "false").lower() == "true"
        )
        
        local_config = LLMProviderConfig(
            model=os.getenv("LOCAL_MODEL", "local-llm"),
            base_url=os.getenv("LOCAL_BASE_URL", "http://localhost:8000"),
            enabled=os.getenv("LOCAL_ENABLED", "false").lower() == "true"
        )
        
        self.llm.providers = {
            "openai": openai_config,
            "anthropic": anthropic_config,
            "local": local_config
        }
        
        # Calendar Providers
        google_calendar_config = CalendarProviderConfig(
            api_key=os.getenv("GOOGLE_CALENDAR_API_KEY", ""),
            calendar_id=os.getenv("GOOGLE_CALENDAR_ID", "primary"),
            enabled=os.getenv("GOOGLE_CALENDAR_ENABLED", "false").lower() == "true"
        )
        
        self.calendar.providers = {
            "google": google_calendar_config,
            "local": CalendarProviderConfig(enabled=True)
        }
        
        # Security
        self.security.secret_key = os.getenv("SECRET_KEY", self.security.secret_key)
        self.security.jwt_secret = os.getenv("JWT_SECRET", self.security.jwt_secret)
        
        # Logging
        self.logging.level = os.getenv("LOG_LEVEL", self.logging.level)
        self.logging.format = os.getenv("LOG_FORMAT", self.logging.format)
    
    def _validate_configuration(self) -> None:
        """Validate the configuration settings."""
        errors = []
        
        # Validate required settings
        if not self.telegram.token:
            errors.append("TELEGRAM_BOT_TOKEN is required")
        
        # Validate at least one LLM provider is enabled
        enabled_llm_providers = [
            name for name, config in self.llm.providers.items() 
            if config.enabled and config.api_key
        ]
        if not enabled_llm_providers:
            logger.warning("No LLM providers are properly configured")
        
        # Validate database configuration in production
        if self.environment == "production":
            if not self.database.password:
                errors.append("Database password is required in production")
            if not self.security.secret_key:
                errors.append("Secret key is required in production")
        
        if errors:
            raise ValueError(f"Configuration validation failed: {', '.join(errors)}")
    
    def _setup_derived_settings(self) -> None:
        """Setup derived settings based on environment and other configurations."""
        # Adjust settings based on environment
        if self.environment == "development":
            self.debug = True
            self.logging.level = "DEBUG"
            self.database.echo = True
        elif self.environment == "production":
            self.debug = False
            self.logging.level = "INFO"
            self.database.echo = False
            self.enable_debug_mode = False
        
        # Setup logging directory
        log_dir = Path(self.logging.log_directory)
        log_dir.mkdir(exist_ok=True)
    
    def get_database_url(self) -> str:
        """Get the database connection URL."""
        return (
            f"postgresql://{self.database.username}:{self.database.password}"
            f"@{self.database.host}:{self.database.port}/{self.database.database}"
        )
    
    def get_llm_config(self) -> Dict[str, Any]:
        """Get LLM service configuration."""
        return {
            "providers": {
                name: {
                    "api_key": config.api_key,
                    "model": config.model,
                    "base_url": config.base_url,
                    "timeout_seconds": config.timeout_seconds,
                    "max_tokens": config.max_tokens,
                    "temperature": config.temperature
                }
                for name, config in self.llm.providers.items()
                if config.enabled
            },
            "provider_priority": self.llm.provider_priority,
            "enable_failover": self.llm.enable_failover,
            "enable_caching": self.llm.enable_caching,
            "max_retries": self.llm.max_retries,
            "timeout_seconds": self.llm.timeout_seconds
        }
    
    def get_calendar_config(self) -> Dict[str, Any]:
        """Get calendar service configuration."""
        return {
            "providers": {
                name: {
                    "api_key": config.api_key,
                    "calendar_id": config.calendar_id
                }
                for name, config in self.calendar.providers.items()
                if config.enabled
            },
            "provider_priority": self.calendar.provider_priority,
            "enable_sync": self.calendar.enable_sync,
            "default_timezone": self.calendar.default_timezone,
            "conflict_resolution": self.calendar.conflict_resolution
        }
    
    def get_preprocessing_config(self) -> Dict[str, Any]:
        """Get preprocessing configuration."""
        return self.preprocessing.copy()
    
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.environment == "development"
    
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment == "production"


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get the global settings instance."""
    return settings


def reload_settings() -> Settings:
    """Reload settings from environment variables."""
    global settings
    settings = Settings()
    return settings
