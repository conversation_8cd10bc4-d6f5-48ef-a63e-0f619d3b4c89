"""
Bot configuration settings.

This module contains configuration settings for the Telegram bot,
including token management and bot behavior settings.
"""

import os
from typing import Op<PERSON>
from pydantic import BaseModel, Field


class BotConfig(BaseModel):
    """Configuration settings for the Telegram bot."""
    
    # Bot token from environment variable
    token: str = Field(
        default_factory=lambda: os.getenv("TELEGRAM_BOT_TOKEN", ""),
        description="Telegram bot token"
    )
    
    # Bot behavior settings
    parse_mode: str = Field(
        default="HTML",
        description="Default parse mode for messages"
    )
    
    # Webhook settings (for production)
    webhook_url: Optional[str] = Field(
        default=None,
        description="Webhook URL for production deployment"
    )
    
    webhook_path: str = Field(
        default="/webhook",
        description="Webhook path"
    )
    
    # Polling settings (for development)
    polling_timeout: int = Field(
        default=30,
        description="Polling timeout in seconds"
    )
    
    # Rate limiting
    rate_limit_calls: int = Field(
        default=30,
        description="Number of calls per rate limit period"
    )
    
    rate_limit_period: int = Field(
        default=60,
        description="Rate limit period in seconds"
    )
    
    def validate_token(self) -> bool:
        """Validate that the bot token is present and properly formatted."""
        if not self.token:
            return False
        
        # Basic validation: Telegram bot tokens are typically in format: 123456:ABC-DEF...
        parts = self.token.split(":")
        if len(parts) != 2:
            return False
            
        try:
            int(parts[0])  # First part should be numeric
            return len(parts[1]) > 10  # Second part should be reasonably long
        except ValueError:
            return False


# Global bot configuration instance
bot_config = BotConfig()
