import os
import async<PERSON>
from typing import List

async def initialize_database(conn) -> None:
    """Initialize the database using SQL schema files."""
    # Read schema SQL file
    schema_path = os.path.join(os.path.dirname(__file__), 'sql', 'schema.sql')
    with open(schema_path, 'r') as f:
        schema_sql = f.read()
    
    # Execute schema creation
    await conn.execute(schema_sql)
    
    print("Database initialization completed successfully.")