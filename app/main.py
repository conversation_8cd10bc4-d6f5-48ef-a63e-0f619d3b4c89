"""
Main application entry point for the AI agent system.

This module orchestrates the initialization and startup of all components
in the new event-driven architecture. It sets up the event bus, initializes
all services, and starts the bot with proper error handling and graceful
shutdown.

Architecture components initialized:
- Event bus system
- Preprocessing pipeline (validation, user identification, context building)
- LLM service with multiple provider support
- Postprocessing pipeline (validation, tool execution, evaluation)
- Bot handlers with event integration
- Configuration and logging systems

The main application ensures all components are properly connected through
the event bus and handles system lifecycle management.
"""

import asyncio
import logging
import signal
import sys
from typing import Optional

from aiogram import Bo<PERSON>, Dispatcher
from aiogram.client.default import DefaultBotProperties
from aiogram.enums import ParseMode

from .config.settings import get_settings
from .utils.logging_config import setup_logging, get_logger, set_component
from .utils.event_bus import event_bus
from .preprocess.preprocessor import Preprocessor
from .postprocess.postprocessor import Postprocessor
from .services.llm_service import LLMService
from .services.calendar_service import CalendarService
from .services.user_service import UserService
from .bot.handlers import MessageHandler

# Setup logging first
setup_logging()
logger = get_logger(__name__)


class ZeitwählApplication:
    """
    Main application class that orchestrates the AI agent system.

    Manages the lifecycle of all components and provides centralized
    error handling and graceful shutdown capabilities.
    """

    def __init__(self):
        """Initialize the application."""
        self.settings = get_settings()
        self.bot: Optional[Bot] = None
        self.dispatcher: Optional[Dispatcher] = None

        # Core services
        self.preprocessor: Optional[Preprocessor] = None
        self.postprocessor: Optional[Postprocessor] = None
        self.llm_service: Optional[LLMService] = None
        self.calendar_service: Optional[CalendarService] = None
        self.user_service: Optional[UserService] = None
        self.message_handler: Optional[MessageHandler] = None

        # Application state
        self.is_running = False
        self.shutdown_event = asyncio.Event()

        logger.info("Zeitwahl AI Agent application initialized")

    async def initialize(self) -> None:
        """Initialize all application components."""
        try:
            set_component("main")
            logger.info("Initializing Zeitwahl AI Agent...")

            # Initialize bot
            await self._initialize_bot()

            # Initialize services
            await self._initialize_services()

            # Initialize processing pipeline
            await self._initialize_pipeline()

            # Initialize handlers
            await self._initialize_handlers()

            # Setup signal handlers for graceful shutdown
            self._setup_signal_handlers()

            logger.info("Application initialization completed successfully")

        except Exception as e:
            logger.error(f"Failed to initialize application: {e}", exc_info=True)
            raise

    async def _initialize_bot(self) -> None:
        """Initialize the Telegram bot."""
        if not self.settings.telegram.token:
            raise ValueError("Telegram bot token is required")

        self.bot = Bot(
            token=self.settings.telegram.token,
            default=DefaultBotProperties(parse_mode=ParseMode.HTML)
        )

        self.dispatcher = Dispatcher()

        logger.info("Telegram bot initialized")

    async def _initialize_services(self) -> None:
        """Initialize core services."""
        # Initialize LLM service
        llm_config = self.settings.get_llm_config()
        self.llm_service = LLMService(llm_config)

        # Initialize calendar service
        calendar_config = self.settings.get_calendar_config()
        self.calendar_service = CalendarService(calendar_config)

        # Initialize user service
        self.user_service = UserService()

        logger.info("Core services initialized")

    async def _initialize_pipeline(self) -> None:
        """Initialize the processing pipeline."""
        # Initialize preprocessor
        preprocessing_config = self.settings.get_preprocessing_config()
        self.preprocessor = Preprocessor(preprocessing_config)

        # Initialize postprocessor
        postprocessing_config = {
            "validator": preprocessing_config.get("response_validator", {}),
            "tool_executor": preprocessing_config.get("tool_executor", {}),
            "evaluator": preprocessing_config.get("response_evaluator", {})
        }
        self.postprocessor = Postprocessor(postprocessing_config)

        logger.info("Processing pipeline initialized")

    async def _initialize_handlers(self) -> None:
        """Initialize message handlers."""
        self.message_handler = MessageHandler()

        # Register handlers with dispatcher
        self.dispatcher.include_router(self.message_handler.get_router())

        logger.info("Message handlers initialized")

    def _setup_signal_handlers(self) -> None:
        """Setup signal handlers for graceful shutdown."""
        if sys.platform != "win32":
            for sig in (signal.SIGTERM, signal.SIGINT):
                signal.signal(sig, self._signal_handler)

    def _signal_handler(self, signum, frame) -> None:
        """Handle shutdown signals."""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        asyncio.create_task(self.shutdown())

    async def start(self) -> None:
        """Start the application."""
        try:
            if not self.bot or not self.dispatcher:
                raise RuntimeError("Application not properly initialized")

            self.is_running = True
            logger.info("Starting Zeitwahl AI Agent...")

            # Start the bot
            if self.settings.telegram.enable_webhook:
                await self._start_webhook()
            else:
                await self._start_polling()

        except Exception as e:
            logger.error(f"Failed to start application: {e}", exc_info=True)
            await self.shutdown()
            raise

    async def _start_webhook(self) -> None:
        """Start the bot with webhook."""
        if not self.settings.telegram.webhook_url:
            raise ValueError("Webhook URL is required for webhook mode")

        logger.info(f"Starting webhook mode: {self.settings.telegram.webhook_url}")

        # Set webhook
        await self.bot.set_webhook(
            url=self.settings.telegram.webhook_url,
            secret_token=self.settings.telegram.webhook_secret
        )

        # Start webhook server (implementation depends on web framework)
        # This would typically involve starting a web server like FastAPI or aiohttp
        logger.warning("Webhook server implementation not included in this example")

    async def _start_polling(self) -> None:
        """Start the bot with polling."""
        logger.info("Starting polling mode...")

        # Delete webhook if set
        await self.bot.delete_webhook(drop_pending_updates=True)

        # Start polling
        await self.dispatcher.start_polling(
            self.bot,
            allowed_updates=self.settings.telegram.allowed_updates,
            timeout=self.settings.telegram.polling_timeout
        )

    async def shutdown(self) -> None:
        """Gracefully shutdown the application."""
        if not self.is_running:
            return

        logger.info("Shutting down Zeitwahl AI Agent...")
        self.is_running = False

        try:
            # Stop the dispatcher
            if self.dispatcher:
                await self.dispatcher.stop_polling()

            # Close bot session
            if self.bot:
                await self.bot.session.close()

            # Clear event bus subscribers
            event_bus.clear_subscribers()

            # Shutdown services
            await self._shutdown_services()

            # Set shutdown event
            self.shutdown_event.set()

            logger.info("Application shutdown completed")

        except Exception as e:
            logger.error(f"Error during shutdown: {e}", exc_info=True)

    async def _shutdown_services(self) -> None:
        """Shutdown all services gracefully."""
        # Clear user service sessions
        if self.user_service:
            self.user_service.clear_inactive_sessions(0)  # Clear all sessions
            self.user_service.clear_user_cache()

        # Clear any service caches or connections
        logger.info("Services shutdown completed")

    async def wait_for_shutdown(self) -> None:
        """Wait for the application to shutdown."""
        await self.shutdown_event.wait()

    def get_health_status(self) -> dict:
        """Get the health status of all components."""
        status = {
            "application": "healthy" if self.is_running else "stopped",
            "components": {}
        }

        if self.preprocessor:
            status["components"]["preprocessor"] = self.preprocessor.get_health_status()

        if self.postprocessor:
            status["components"]["postprocessor"] = self.postprocessor.get_health_status()

        if self.llm_service:
            status["components"]["llm_service"] = self.llm_service.get_provider_status()

        if self.calendar_service:
            status["components"]["calendar_service"] = self.calendar_service.get_provider_status()

        if self.user_service:
            status["components"]["user_service"] = self.user_service.get_service_stats()

        return status


async def main() -> None:
    """Main entry point for the application."""
    app = ZeitwählApplication()

    try:
        # Initialize the application
        await app.initialize()

        # Start the application
        await app.start()

    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Application error: {e}", exc_info=True)
        sys.exit(1)
    finally:
        # Ensure cleanup
        await app.shutdown()


if __name__ == "__main__":
    # Run the application
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}", exc_info=True)
        sys.exit(1)

