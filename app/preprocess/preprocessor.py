"""
Main preprocessor orchestrator for the AI agent.

This module coordinates all preprocessing components to transform incoming
messages into structured prompts ready for LLM processing. It orchestrates
the entire preprocessing pipeline and publishes events for the next stage.

Pipeline stages:
1. Message validation and sanitization
2. User identification and context loading
3. Context building from multiple sources
4. Prompt construction with all components
5. Event publishing for LLM processing

The preprocessor ensures that all preprocessing steps are executed in the
correct order and handles errors gracefully with appropriate fallbacks.
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime

from .message_validator import MessageValidator, ValidationResult
from .user_identifier import UserIdentifier
from .context_builder import ContextBuilder
from .prompt_builder import PromptBuilder
from ..utils.events import MessageReceived, MessagePreprocessed, ErrorOccurred
from ..utils.event_bus import event_bus

logger = logging.getLogger(__name__)


class Preprocessor:
    """
    Main preprocessor that orchestrates the entire preprocessing pipeline.
    
    Coordinates message validation, user identification, context building,
    and prompt construction to prepare messages for LLM processing.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the preprocessor with all required components.
        
        Args:
            config: Configuration dictionary for preprocessing components
        """
        # Initialize all preprocessing components
        self.message_validator = MessageValidator(config.get('validator', {}) if config else {})
        self.user_identifier = UserIdentifier()
        self.context_builder = ContextBuilder(config=config.get('context_builder', {}) if config else {})
        self.prompt_builder = PromptBuilder(config.get('prompt_builder', {}) if config else {})
        
        # Subscribe to message received events using the new event bus
        event_bus.subscribe_tagged_methods(self)
        
        logger.info("Preprocessor initialized and subscribed to MessageReceived events")
    
    @event_bus.subscribe("MessageReceived")
    async def process_message(self, event: MessageReceived) -> None:
        """
        Process an incoming message through the complete preprocessing pipeline.
        
        Args:
            event: MessageReceived event containing the message to process
        """
        try:
            logger.info(f"Starting preprocessing for message from user {event.user_id}")
            
            # Stage 1: Message validation
            validation_result = await self._validate_message(event)
            if not validation_result.is_valid:
                await self._handle_validation_error(event, validation_result)
                return
            
            # Stage 2: User identification
            user_context = await self._identify_user(event)
            if not user_context:
                await self._handle_user_identification_error(event)
                return
            
            # Stage 3: Context building
            context = await self._build_context(user_context, validation_result.sanitized_message)
            if not context:
                await self._handle_context_building_error(event, user_context)
                return
            
            # Stage 4: Prompt building
            prompt = await self._build_prompt(validation_result.sanitized_message, context, user_context)
            if not prompt:
                await self._handle_prompt_building_error(event, user_context)
                return
            
            # Stage 5: Publish preprocessed message event
            await self._publish_preprocessed_event(event, user_context, context, prompt, validation_result)
            
            logger.info(f"Successfully completed preprocessing for user {event.user_id}")
            
        except Exception as e:
            logger.error(f"Unexpected error in preprocessing pipeline: {e}", exc_info=True)
            await self._handle_unexpected_error(event, e)
    
    async def _validate_message(self, event: MessageReceived) -> ValidationResult:
        """
        Validate the incoming message.
        
        Args:
            event: MessageReceived event
            
        Returns:
            ValidationResult with validation status and sanitized message
        """
        try:
            logger.debug(f"Validating message from user {event.user_id}")
            
            validation_result = await self.message_validator.validate_message(
                user_id=event.user_id,
                message=event.message_text,
                metadata={
                    'chat_id': event.chat_id,
                    'message_id': event.message_id,
                    'platform': event.platform,
                    'timestamp': event.timestamp.isoformat()
                }
            )
            
            if validation_result.warnings:
                logger.warning(f"Message validation warnings for user {event.user_id}: {validation_result.warnings}")
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Error during message validation: {e}", exc_info=True)
            # Return failed validation result
            return ValidationResult(
                is_valid=False,
                validation_errors=[f"Validation error: {str(e)}"]
            )
    
    async def _identify_user(self, event: MessageReceived) -> Optional[Dict[str, Any]]:
        """
        Identify the user and load their context.
        
        Args:
            event: MessageReceived event
            
        Returns:
            User context dictionary or None if identification fails
        """
        try:
            logger.debug(f"Identifying user {event.user_id}")
            
            # Create a mock Telegram message object for the user identifier
            # In a complete implementation, this would be the actual message object
            mock_message = type('MockMessage', (), {
                'from_user': type('MockUser', (), {
                    'id': event.user_id,
                    'username': None,
                    'first_name': f"User{event.user_id}",
                    'last_name': None,
                    'language_code': 'en'
                })(),
                'chat': type('MockChat', (), {
                    'id': event.chat_id,
                    'type': 'private'
                })(),
                'message_id': event.message_id
            })()
            
            user_context = await self.user_identifier.identify_user_from_message(mock_message)
            
            if user_context:
                logger.debug(f"Successfully identified user {event.user_id}")
            else:
                logger.warning(f"Failed to identify user {event.user_id}")
            
            return user_context
            
        except Exception as e:
            logger.error(f"Error during user identification: {e}", exc_info=True)
            return None
    
    async def _build_context(self, user_context: Dict[str, Any], message: str) -> Optional[Dict[str, Any]]:
        """
        Build context for the message.
        
        Args:
            user_context: User identification and session information
            message: Sanitized message text
            
        Returns:
            Context dictionary or None if building fails
        """
        try:
            logger.debug(f"Building context for user {user_context.get('user', {}).get('id', 'unknown')}")
            
            context = await self.context_builder.build_context(user_context, message)
            
            if context:
                logger.debug("Successfully built context")
            else:
                logger.warning("Failed to build context")
            
            return context
            
        except Exception as e:
            logger.error(f"Error during context building: {e}", exc_info=True)
            return None
    
    async def _build_prompt(self, message: str, context: Dict[str, Any], user_context: Dict[str, Any]) -> Optional[str]:
        """
        Build the final prompt for LLM processing.
        
        Args:
            message: Sanitized message text
            context: Context information
            user_context: User identification information
            
        Returns:
            Complete prompt string or None if building fails
        """
        try:
            logger.debug("Building prompt for LLM processing")
            
            prompt = await self.prompt_builder.build_prompt(message, context, user_context)
            
            if prompt:
                logger.debug(f"Successfully built prompt with {len(prompt)} characters")
            else:
                logger.warning("Failed to build prompt")
            
            return prompt
            
        except Exception as e:
            logger.error(f"Error during prompt building: {e}", exc_info=True)
            return None
    
    async def _publish_preprocessed_event(self, 
                                        original_event: MessageReceived,
                                        user_context: Dict[str, Any],
                                        context: Dict[str, Any],
                                        prompt: str,
                                        validation_result: ValidationResult) -> None:
        """
        Publish the MessagePreprocessed event.
        
        Args:
            original_event: Original MessageReceived event
            user_context: User identification information
            context: Built context information
            prompt: Complete prompt for LLM
            validation_result: Message validation results
        """
        try:
            # Extract conversation history from context
            conversation_history = context.get('conversation_history', [])
            
            # Extract available tools from context
            available_tools = [tool.get('name', 'unknown') for tool in context.get('available_tools', [])]
            
            # Create preprocessing metadata
            preprocessing_metadata = {
                'validation_warnings': validation_result.warnings,
                'context_build_timestamp': context.get('metadata', {}).get('context_build_timestamp'),
                'prompt_length': len(prompt),
                'preprocessing_completed_at': datetime.now().isoformat()
            }
            
            # Publish the preprocessed event
            preprocessed_event = MessagePreprocessed(
                user_id=original_event.user_id,
                chat_id=original_event.chat_id,
                original_message=original_event.message_text,
                prepared_prompt=prompt,
                context=context,
                available_tools=available_tools,
                conversation_history=conversation_history,
                preprocessing_metadata=preprocessing_metadata
            )
            
            await event_bus.publish(preprocessed_event)
            logger.info(f"Published MessagePreprocessed event for user {original_event.user_id}")
            
        except Exception as e:
            logger.error(f"Error publishing preprocessed event: {e}", exc_info=True)
            await self._handle_unexpected_error(original_event, e)
    
    async def _handle_validation_error(self, event: MessageReceived, validation_result: ValidationResult) -> None:
        """Handle message validation errors."""
        error_message = f"Message validation failed: {', '.join(validation_result.validation_errors or [])}"
        
        await event_bus.publish(ErrorOccurred(
            user_id=event.user_id,
            chat_id=event.chat_id,
            error_type="validation_error",
            error_message=error_message,
            component="preprocessor.message_validator",
            context={'validation_result': validation_result.__dict__},
            timestamp=datetime.now()
        ))
        
        logger.warning(f"Message validation failed for user {event.user_id}: {error_message}")
    
    async def _handle_user_identification_error(self, event: MessageReceived) -> None:
        """Handle user identification errors."""
        error_message = "Failed to identify user from message"
        
        await event_bus.publish(ErrorOccurred(
            user_id=event.user_id,
            chat_id=event.chat_id,
            error_type="user_identification_error",
            error_message=error_message,
            component="preprocessor.user_identifier",
            timestamp=datetime.now()
        ))
        
        logger.error(f"User identification failed for user {event.user_id}")
    
    async def _handle_context_building_error(self, event: MessageReceived, user_context: Dict[str, Any]) -> None:
        """Handle context building errors."""
        error_message = "Failed to build context for message processing"
        
        await event_bus.publish(ErrorOccurred(
            user_id=event.user_id,
            chat_id=event.chat_id,
            error_type="context_building_error",
            error_message=error_message,
            component="preprocessor.context_builder",
            context={'user_context_available': bool(user_context)},
            timestamp=datetime.now()
        ))
        
        logger.error(f"Context building failed for user {event.user_id}")
    
    async def _handle_prompt_building_error(self, event: MessageReceived, user_context: Dict[str, Any]) -> None:
        """Handle prompt building errors."""
        error_message = "Failed to build prompt for LLM processing"
        
        await event_bus.publish(ErrorOccurred(
            user_id=event.user_id,
            chat_id=event.chat_id,
            error_type="prompt_building_error",
            error_message=error_message,
            component="preprocessor.prompt_builder",
            timestamp=datetime.now()
        ))
        
        logger.error(f"Prompt building failed for user {event.user_id}")
    
    async def _handle_unexpected_error(self, event: MessageReceived, error: Exception) -> None:
        """Handle unexpected errors in the preprocessing pipeline."""
        await event_bus.publish(ErrorOccurred(
            user_id=event.user_id,
            chat_id=event.chat_id,
            error_type="unexpected_error",
            error_message=str(error),
            component="preprocessor",
            stack_trace=str(error.__traceback__) if error.__traceback__ else None,
            timestamp=datetime.now()
        ))
        
        logger.error(f"Unexpected error in preprocessing for user {event.user_id}: {error}")
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get health status of the preprocessor and its components."""
        return {
            'status': 'healthy',
            'components': {
                'message_validator': 'healthy',
                'user_identifier': 'healthy',
                'context_builder': 'healthy',
                'prompt_builder': 'healthy'
            },
            'last_check': datetime.now().isoformat()
        }
