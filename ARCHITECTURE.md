# Zeitwahl AI Agent - Event-Driven Architecture

This document describes the new event-driven architecture for the Zeitwahl AI agent system, designed following KISS principles with clear separation of concerns and decoupled components.

## Architecture Overview

The system is built around an event bus that enables loose coupling between components. Each component publishes and subscribes to events, creating a reactive system that's easy to test, maintain, and extend.

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Bot Handler   │    │  Preprocessor   │    │  LLM Service    │
│                 │    │                 │    │                 │
│ • Message Input │───▶│ • Validation    │───▶│ • OpenAI        │
│ • Commands      │    │ • User ID       │    │ • Anthropic     │
│ • Event Pub     │    │ • Context Build │    │ • Local Models  │
└─────────────────┘    │ • Prompt Build  │    └─────────────────┘
                       └─────────────────┘             │
                                │                      │
                                ▼                      ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   Event Bus     │    │ Postprocessor   │
                       │                 │    │                 │
                       │ • Pub/Sub       │◀───│ • Validation    │
                       │ • Decorators    │    │ • Tool Exec     │
                       │ • Tagged Methods│    │ • Evaluation    │
                       └─────────────────┘    └─────────────────┘
```

## Core Components

### 1. Event Bus (`app/utils/event_bus.py`)

The heart of the system, providing:
- **Decorator-based subscriptions**: `@event_bus.subscribe("EventName")`
- **Tagged method auto-discovery**: Automatically finds and subscribes methods
- **Priority-based ordering**: Higher priority subscribers execute first
- **Error isolation**: Failures in one handler don't affect others
- **Event registry**: Complete audit trail of all events

```python
class MyHandler:
    @event_bus.subscribe("MessageReceived")
    async def handle_message(self, event):
        # Process the event
        pass

# Auto-subscribe all tagged methods
handler = MyHandler()
event_bus.subscribe_tagged_methods(handler)
```

### 2. Preprocessing Pipeline (`app/preprocess/`)

Transforms raw messages into structured prompts:

#### Message Validator (`message_validator.py`)
- Input sanitization and validation
- Rate limiting and spam detection
- Content filtering and safety checks

#### User Identifier (`user_identifier.py`)
- User recognition and context loading
- Session management and tracking
- User preference integration

#### Context Builder (`context_builder.py`)
- Conversation history aggregation
- External API data integration (calendar, weather)
- Temporal context (time, date, timezone)

#### Prompt Builder (`prompt_builder.py`)
- Structured prompt construction
- Tool definition inclusion
- Context optimization and truncation

#### Preprocessor (`preprocessor.py`)
- Orchestrates the entire preprocessing pipeline
- Publishes `MessagePreprocessed` events

### 3. LLM Service (`app/services/llm_service.py`)

Manages multiple LLM providers with:
- **Multi-provider support**: OpenAI, Anthropic, local models
- **Automatic failover**: Switches providers on failure
- **Response streaming**: Real-time response generation
- **Token optimization**: Efficient prompt and response handling

### 4. Postprocessing Pipeline (`app/postprocess/`)

Processes LLM responses and executes actions:

#### Response Validator (`response_validator.py`)
- Content safety and appropriateness checks
- Format and structure validation
- Quality assessment and scoring

#### Tool Executor (`tool_executor.py`)
- Secure tool execution environment
- Rate limiting and timeout handling
- Parallel and sequential execution modes

#### Response Evaluator (`response_evaluator.py`)
- Multi-dimensional quality assessment
- User satisfaction prediction
- Continuous improvement metrics

#### Postprocessor (`postprocessor.py`)
- Orchestrates the postprocessing pipeline
- Publishes `ResponseReady` events

### 5. Services (`app/services/`)

Business logic and external integrations:

#### Calendar Service (`calendar_service.py`)
- Multi-provider calendar support (Google, Outlook, local)
- Event CRUD operations
- Conflict detection and resolution

#### User Service (`user_service.py`)
- Enhanced user management
- Session tracking and analytics
- Preference management

### 6. Configuration (`app/config/`)

Comprehensive configuration management:
- Environment-based settings
- Component-specific configurations
- Validation and type checking
- Secret management

## Event Flow

1. **Message Reception**: Bot handler receives Telegram message
2. **Event Publication**: `MessageReceived` event published to event bus
3. **Preprocessing**: Preprocessor validates, identifies user, builds context and prompt
4. **LLM Processing**: LLM service generates response
5. **Postprocessing**: Response validated, tools executed, quality evaluated
6. **Response Delivery**: Final response sent back to user

## Key Features

### Event-Driven Architecture
- **Loose Coupling**: Components communicate only through events
- **Scalability**: Easy to add new components and handlers
- **Testability**: Each component can be tested in isolation
- **Monitoring**: Complete event audit trail

### KISS Principle Adherence
- **Simple Interfaces**: Clear, focused component responsibilities
- **Minimal Dependencies**: Each component has minimal external dependencies
- **Easy Configuration**: Environment-based configuration with sensible defaults
- **Straightforward Testing**: Simple unit and integration testing

### Extensibility
- **Plugin Architecture**: Easy to add new LLM providers, tools, or services
- **Event Middleware**: Add cross-cutting concerns like logging, metrics
- **Service Discovery**: Automatic registration of tagged methods

## Usage Examples

### Basic Event Handling
```python
from app.utils.event_bus import event_bus
from app.utils.events import MessageReceived

class MyHandler:
    @event_bus.subscribe("MessageReceived")
    async def handle_message(self, event: MessageReceived):
        print(f"Received: {event.message_text}")

# Register handler
handler = MyHandler()
event_bus.subscribe_tagged_methods(handler)

# Publish event
await event_bus.publish(MessageReceived(
    user_id=123,
    chat_id=456,
    message_text="Hello, world!",
    # ... other fields
))
```

### Service Integration
```python
from app.services.calendar_service import CalendarService

# Initialize service
calendar = CalendarService({
    "providers": {"google": {"api_key": "your-key"}},
    "default_timezone": "UTC"
})

# Create event
event = await calendar.create_event(
    title="Meeting",
    start_time=datetime.now(),
    description="Important meeting"
)
```

## Running the System

### Development
```bash
# Set environment variables
export TELEGRAM_BOT_TOKEN="your-bot-token"
export OPENAI_API_KEY="your-openai-key"

# Run the demo
python example_usage.py

# Run the full application
python -m app.main
```

### Production
```bash
# Set production environment
export ENVIRONMENT=production
export DB_HOST=your-db-host
export DB_PASSWORD=your-db-password

# Run with proper logging
python -m app.main
```

## Testing

The architecture supports comprehensive testing:

```python
# Test individual components
pytest app/preprocess/test_message_validator.py

# Test event flows
pytest app/tests/test_event_flows.py

# Integration tests
pytest app/tests/test_integration.py
```

## Monitoring and Debugging

### Event Registry
```python
# Get event history
registry = event_bus.get_registry()
for event in registry[-10:]:  # Last 10 events
    print(f"{event['action']}: {event['event_type']}")
```

### Health Checks
```python
# Check component health
from app.main import ZeitwählApplication

app = ZeitwählApplication()
await app.initialize()
health = app.get_health_status()
print(health)
```

## Migration from Old Architecture

The new architecture maintains compatibility with existing models and database schemas while providing:
- Better separation of concerns
- Improved testability
- Enhanced monitoring capabilities
- Easier maintenance and extension

Key migration benefits:
- **Reduced Complexity**: Clear component boundaries
- **Better Error Handling**: Isolated failure domains
- **Improved Performance**: Asynchronous event processing
- **Enhanced Debugging**: Complete event audit trail

This architecture provides a solid foundation for building scalable, maintainable AI agent systems while adhering to KISS principles and modern software engineering best practices.
