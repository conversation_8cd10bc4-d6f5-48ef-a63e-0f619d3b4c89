# Zeitwahl Telegram Bot

This document describes the Telegram bot implementation for the Zeitwahl application.

## Overview

The bot is built using [aiogram](https://docs.aiogram.dev/) v3.x and provides a simple greeting functionality that:
1. Identifies users from incoming messages
2. Responds with personalized greetings using the format "Hello {user}"
3. Supports both regular messages and the `/start` command

## Architecture

The bot implementation follows a clean, modular architecture:

```
app/bot/
├── __init__.py          # Package initialization
├── bot.py              # Main TelegramBot class
├── handlers.py         # Message and command handlers
└── user_service.py     # User identification and management

app/config/
├── schema.py           # Database schema constants
└── bot_config.py       # Bot configuration settings

tests/unit/bot/
├── __init__.py
├── test_bot.py         # Tests for main bot class
├── test_handlers.py    # Tests for message handlers
└── test_user_service.py # Tests for user service
```

## Components

### TelegramBot (`app/bot/bot.py`)
- Main bot class that initializes aiogram Bot and Dispatcher
- Handles bot lifecycle (start, stop)
- Manages configuration and token validation
- Provides message sending functionality

### MessageHandler (`app/bot/handlers.py`)
- Processes incoming text messages and `/start` commands
- Uses aiogram Router for handler registration
- Integrates with UserService for user identification

### UserService (`app/bot/user_service.py`)
- Identifies users from Telegram messages
- Creates User model instances from Telegram user data
- Formats greeting messages with user names

### BotConfig (`app/config/bot_config.py`)
- Manages bot configuration settings
- Handles environment variable loading
- Provides token validation

## Setup and Installation

### 1. Install Dependencies

The bot requires aiogram, which is already added to the project dependencies:

```bash
# Install dependencies using uv (recommended)
uv sync

# Or using pip
pip install aiogram>=3.15.0
```

### 2. Get a Bot Token

1. Message [@BotFather](https://t.me/botfather) on Telegram
2. Create a new bot with `/newbot`
3. Follow the instructions to get your bot token
4. The token will look like: `123456789:ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijk`

### 3. Set Environment Variable

```bash
export TELEGRAM_BOT_TOKEN="your_bot_token_here"
```

### 4. Run the Bot

```bash
# Using the provided runner script
python run_bot.py

# Or directly
python -m app.bot.bot
```

## Usage

Once the bot is running:

1. Find your bot on Telegram using the username you set with BotFather
2. Send `/start` to get a welcome message
3. Send any text message to receive a personalized greeting

### Example Interactions

```
User: /start
Bot: Hello John! Welcome to Zeitwahl bot. Send me any message and I'll greet you!

User: Hi there!
Bot: Hello John

User: How are you?
Bot: Hello John
```

## Testing

The bot includes comprehensive unit tests covering all components:

```bash
# Run all bot tests
python tests/run_tests.py bot

# Run specific test files
python tests/unit/bot/test_bot.py
python tests/unit/bot/test_handlers.py
python tests/unit/bot/test_user_service.py

# Using pytest (if available)
pytest tests/unit/bot/
```

### Test Coverage

- **Bot initialization and configuration**
- **Token validation**
- **Message handling (success and failure cases)**
- **User identification from messages**
- **Greeting message formatting**
- **Integration between components**

## Configuration

### Environment Variables

- `TELEGRAM_BOT_TOKEN`: Your bot token (required)

### Bot Settings

The bot configuration can be customized in `app/config/bot_config.py`:

- `parse_mode`: Default message parse mode (HTML)
- `polling_timeout`: Polling timeout in seconds (30)
- `rate_limit_calls`: Rate limiting settings (30 calls per minute)

## Error Handling

The bot includes robust error handling:

- **Invalid tokens**: Validates token format on startup
- **Missing users**: Handles messages without user information
- **Network errors**: Graceful handling of connection issues
- **Logging**: Comprehensive logging for debugging

## Integration with Existing Models

The bot integrates seamlessly with the existing User model:

- Uses the same `User` class from `app.model.user`
- Follows the same validation patterns
- Compatible with the repository pattern (when implemented)
- Maintains consistency with database schema

## Development Notes

### Following KISS Principle

The implementation follows the KISS (Keep It Simple, Stupid) principle:
- Clear separation of concerns
- Simple, focused classes
- Minimal dependencies
- Easy to understand and maintain

### Future Enhancements

The current implementation provides a foundation for:
- Database integration for user persistence
- More complex conversation flows
- Calendar integration features
- Command handling for calendar operations
- Webhook support for production deployment

## Troubleshooting

### Common Issues

1. **"Bot token is required" error**
   - Make sure `TELEGRAM_BOT_TOKEN` environment variable is set
   - Verify the token is correct and not expired

2. **"Invalid bot token format" error**
   - Check that your token follows the format: `number:letters_and_numbers`
   - Ensure there are no extra spaces or characters

3. **Bot doesn't respond**
   - Check that the bot is running without errors
   - Verify the bot token is valid
   - Make sure you're messaging the correct bot

4. **Import errors in tests**
   - Ensure you're running tests from the project root directory
   - Check that all dependencies are installed

### Logging

The bot uses Python's logging module. To see debug information:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```
