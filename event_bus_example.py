import asyncio
import traceback
import typing
from collections import defaultdict
from functools import wraps
import logging
import sys

logger = logging.getLogger(__name__)

class EventBus:
    """
    EventBus class for event-driven communication among components.
    """
    def __init__(self):
        """
        Initialize the EventBus lists.
        """
        self.listeners = defaultdict(list)
        self.registry = []

    def subscribe(self, topic) -> typing.Callable:
        """
        Decorator to subscribe a method to a specific event topic.
        Args:
            topic (str): The event topic to subscribe to.
        Returns:
            callable: The decorator function.
        """
        def decorator(function):
            @wraps(function)
            async def wrapper(self, *args, **kwargs):
                return await function(self, *args, **kwargs)
            function.__tag_for_sub__ = topic
            return function
        return decorator

    def subscribe_to_topic(self, event_type, callback, priority=0):
        """
        Subscribe a callback function to a specific event topic.
        Args:
            event_type (str): The event topic to subscribe to.
            callback (callable): The function to be called when the event is published.
            priority (int, optional): Priority of the subscription. Higher values indicate higher priority.
        Returns:
            None
        """
        logger.debug(f"Subscribing {callback} to {event_type}")
        self.listeners[event_type].append((callback, priority))
        self.listeners[event_type].sort(key=lambda item: item[1], reverse=True)

        subscription = {
            "event_type": event_type,
            "action": "subscribe",
            "timestamp": asyncio.get_event_loop().time(),
            "callback": callback
        }
        self.registry.append(subscription)

    def subscribe_tagged_methods(self, obj):
        """
        Subscribe methods of an object tagged with an event topic.
        Args:
            obj (object): The object whose tagged methods are to be subscribed.
        Returns:
            None
        """
        for name in dir(obj):
            method = getattr(obj, name)
            if hasattr(method, "__tag_for_sub__"):
                self.subscribe_to_topic(method.__tag_for_sub__, method)

    def unsubscribe(self, event_type, callback):
        """
        Unsubscribe a callback function from a specific event topic.
        Args:
            event_type (str): The event topic to unsubscribe from.
            callback (callable): The function to be unsubscribed.
        Returns:
            None
        """
        if event_type in self.listeners:
            self.listeners[event_type] = [
                (cb, prio) for cb, prio in self.listeners[event_type] if cb != callback
            ]

            unsubscription = {
                "event_type": event_type,
                "action": "unsubscribe",
                "timestamp": asyncio.get_event_loop().time(),
                "callback": callback
            }
            self.registry.append(unsubscription)

    def unsubscribe_tagged_methods(self, obj):
        """
        Subscribe methods of an object tagged with an event topic.
        Args:
            obj (object): The object whose tagged methods are to be subscribed.
        Returns:
            None
        """
        for name in dir(obj):
            method = getattr(obj, name)
            if hasattr(method, "__tag_for_sub__"):
                self.unsubscribe(method.__tag_for_sub__, method)

    async def publish(self, event_type, message=None):
        """
        Publish an event to all subscribed listeners.
        Args:
            event_type (str): The event topic to publish.
            message (Any, optional): Data to be sent with the event.
        Returns:
            None
        """
        try:
            if event_type in self.listeners:
                event = {
                    "event_type": event_type,
                    "action": "publish",
                    "timestamp": asyncio.get_event_loop().time(),
                    "data": message
                }
                self.registry.append(event)

                tasks = [asyncio.create_task(self._fire_event(callback, message)) for callback, _ in self.listeners[event_type]]
                await asyncio.sleep(0)
                await asyncio.gather(*tasks, return_exceptions=True)
        except:
            traceback.print_exc()

    async def send(self, event_type, message=None):
        """
        Send an event to a single subscriber and return a Future for the result.
        Args:
            event_type (str): The event topic to send.
            message (Any, optional): Data to be sent with the event.
        Returns:
            asyncio.Future: Future for the result of the subscribed function.
        """
        try:
            if event_type in self.listeners:
                callback, _ = self.listeners[event_type][0]  # Get the first subscriber

                # Create a future for the result
                result_future = asyncio.Future()

                # Define a callback function to set the result when the event is fired
                async def on_event_fired(result):
                    result_future.set_result(result)

                # Fire the event in the background
                asyncio.create_task(self._fire_event(callback, message)).add_done_callback(
                    lambda task: asyncio.create_task(on_event_fired(task.result()))
                )

                return result_future
        except:
            traceback.print_exc()

    async def _fire_event(self, callback, message):
        try:
            return await callback(message)
        except Exception as e:
            logger.error(traceback.format_exc())
            logger.error(f"Exception in event handler: {e}")
            sys.exit()

    def get_registry(self):
        return self.registry
