#!/usr/bin/env python3
"""
Example usage of the new event-driven AI agent architecture.

This script demonstrates how to use the restructured Zeitwahl AI agent
with the new event bus system, preprocessing/postprocessing pipelines,
and service-oriented architecture.

Run this script to see the event-driven architecture in action:
    python example_usage.py
"""

import asyncio
import logging
from datetime import datetime

# Setup basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

from app.utils.event_bus import event_bus
from app.utils.events import MessageReceived, MessagePreprocessed, LLMResponseReceived, ResponseReady
from app.utils.constants import Platform
from app.preprocess.preprocessor import Preprocessor
from app.postprocess.postprocessor import Postprocessor
from app.services.llm_service import LLMService


class ExampleEventHandler:
    """Example event handler to demonstrate the event bus system."""
    
    def __init__(self):
        """Initialize the example handler."""
        # Subscribe to events using decorators
        event_bus.subscribe_tagged_methods(self)
        print("Example event handler initialized and subscribed to events")
    
    @event_bus.subscribe("MessageReceived")
    async def handle_message_received(self, event: MessageReceived):
        """Handle incoming message events."""
        print(f"📨 Message received from user {event.user_id}: {event.message_text[:50]}...")
    
    @event_bus.subscribe("MessagePreprocessed")
    async def handle_message_preprocessed(self, event: MessagePreprocessed):
        """Handle preprocessed message events."""
        print(f"🔄 Message preprocessed for user {event.user_id}, prompt length: {len(event.prepared_prompt)}")
    
    @event_bus.subscribe("LLMResponseReceived")
    async def handle_llm_response(self, event: LLMResponseReceived):
        """Handle LLM response events."""
        print(f"🤖 LLM response received for user {event.user_id}: {event.llm_response[:50]}...")
    
    @event_bus.subscribe("ResponseReady")
    async def handle_response_ready(self, event: ResponseReady):
        """Handle final response events."""
        print(f"✅ Final response ready for user {event.user_id}: {event.response_text[:50]}...")


async def demonstrate_event_driven_architecture():
    """Demonstrate the event-driven architecture with a sample message flow."""
    
    print("🚀 Starting Zeitwahl AI Agent Architecture Demo")
    print("=" * 60)
    
    # Initialize components
    print("\n📋 Initializing components...")
    
    # Create example event handler
    example_handler = ExampleEventHandler()
    
    # Initialize preprocessing pipeline
    preprocessor = Preprocessor({
        "validator": {
            "max_message_length": 1000,
            "min_message_length": 1,
            "required_sanitization": True
        },
        "context_builder": {
            "max_conversation_history": 5,
            "include_temporal_context": True
        },
        "prompt_builder": {
            "max_prompt_length": 8000,
            "prompt_format": "structured"
        }
    })
    
    # Initialize LLM service
    llm_service = LLMService({
        "providers": {
            "local": {
                "model": "demo-model",
                "base_url": "http://localhost:8000",
                "enabled": True
            }
        },
        "provider_priority": ["local"],
        "enable_failover": False
    })
    
    # Initialize postprocessing pipeline
    postprocessor = Postprocessor({
        "validator": {
            "max_response_length": 2000,
            "check_content_safety": True
        },
        "tool_executor": {
            "max_concurrent_executions": 3,
            "enable_rate_limiting": True
        },
        "evaluator": {
            "minimum_acceptable_score": 0.7
        }
    })
    
    print("✅ All components initialized successfully!")
    
    # Simulate message processing
    print("\n🔄 Simulating message processing flow...")
    print("-" * 40)
    
    # Create a sample message
    sample_message = MessageReceived(
        user_id=12345,
        chat_id=67890,
        message_text="Schedule a meeting with John tomorrow at 2 PM about the project review",
        message_id="msg_001",
        timestamp=datetime.now(),
        platform=Platform.TELEGRAM.value,
        metadata={
            "username": "demo_user",
            "first_name": "Demo",
            "language_code": "en"
        }
    )
    
    # Publish the message to start the processing pipeline
    print(f"📤 Publishing message: '{sample_message.message_text}'")
    await event_bus.publish(sample_message)
    
    # Wait a bit for processing
    await asyncio.sleep(2)
    
    print("\n📊 Event Bus Statistics:")
    print(f"   - Total subscribers: {event_bus.get_subscriber_count()}")
    print(f"   - Event registry entries: {len(event_bus.get_registry())}")
    
    # Show some recent events from registry
    registry = event_bus.get_registry()
    if registry:
        print("\n📝 Recent events:")
        for event in registry[-5:]:  # Show last 5 events
            print(f"   - {event['action']}: {event['event_type']} at {event['timestamp']:.2f}")
    
    print("\n🎉 Demo completed successfully!")
    print("=" * 60)


async def demonstrate_service_integration():
    """Demonstrate service integration and tool execution."""
    
    print("\n🔧 Service Integration Demo")
    print("-" * 30)
    
    # Import services
    from app.services.calendar_service import CalendarService
    from app.services.user_service import UserService
    
    # Initialize services
    calendar_service = CalendarService({
        "providers": {
            "local": {"enabled": True}
        },
        "default_timezone": "UTC"
    })
    
    user_service = UserService()
    
    print("✅ Services initialized")
    
    # Demonstrate calendar operations
    print("\n📅 Calendar Operations:")
    
    # Create a sample event
    event = await calendar_service.create_event(
        title="Demo Meeting",
        start_time=datetime.now(),
        description="This is a demo calendar event",
        location="Conference Room A"
    )
    
    print(f"   - Created event: {event.title} (ID: {event.id})")
    
    # Search for events
    events = await calendar_service.search_events("Demo")
    print(f"   - Found {len(events)} events matching 'Demo'")
    
    # Get upcoming events
    upcoming = await calendar_service.get_upcoming_events(days_ahead=7)
    print(f"   - {len(upcoming)} upcoming events in next 7 days")
    
    print("\n👤 User Service Operations:")
    
    # Get service stats
    stats = user_service.get_service_stats()
    print(f"   - Active sessions: {stats['active_sessions']}")
    print(f"   - Cached users: {stats['cached_users']}")
    
    print("✅ Service integration demo completed!")


async def main():
    """Main demo function."""
    try:
        # Run the architecture demo
        await demonstrate_event_driven_architecture()
        
        # Run the service integration demo
        await demonstrate_service_integration()
        
        print("\n🎯 All demos completed successfully!")
        print("\nTo run the full application:")
        print("1. Set your TELEGRAM_BOT_TOKEN environment variable")
        print("2. Run: python -m app.main")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Clean up
        event_bus.clear_subscribers()
        print("\n🧹 Cleaned up event bus subscribers")


if __name__ == "__main__":
    print("🤖 Zeitwahl AI Agent - Event-Driven Architecture Demo")
    print("This demo showcases the new event bus system and service architecture")
    print()
    
    # Run the demo
    asyncio.run(main())
