Coding Conventions and Rules:
1. Clear Code
2. Use self-documenting names (no abbreviations)
3. Modularize: Use interfaces, Seperation of concerns
4. SRP, DRY, KISS
5. Comments everything, explaining the purpose and functionality of the code in natural language, commit style
6. Zero external libraries, unless necessary
7. Validate all inputs
8. Conciseness over readability
9. Must use async
11. All the above must not be abused. Use when necessary and appropriate.